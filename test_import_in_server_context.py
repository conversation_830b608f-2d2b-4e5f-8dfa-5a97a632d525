#!/usr/bin/env python3
"""
Test the simple SWE executor import in the same context as the API server.
"""

import sys
import os
import logging

# Add the project root to the path (same as the API server)
sys.path.insert(0, '/u/Arun Dev/Python Projects/AI-Coding-Agent')

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_import_in_server_context():
    """Test import in the same context as the API server."""
    print("🧪 Testing Simple SWE Executor Import in Server Context")
    print("=" * 60)
    
    try:
        print("📦 Testing basic imports...")
        
        # Test basic imports first
        import bridge
        print("✅ bridge package imported")
        
        import bridge.core
        print("✅ bridge.core package imported")
        
        # Test the problematic import
        print("\n📦 Testing simple_swe_executor import...")
        from bridge.core.simple_swe_executor import get_simple_swe_executor, SimpleSWEConfig
        print("✅ simple_swe_executor imported successfully")
        
        # Test executor creation
        print("\n🔧 Testing executor creation...")
        executor = get_simple_swe_executor()
        print("✅ Executor created successfully")
        
        # Test executor properties
        print(f"📁 Project root: {executor.project_root}")
        print(f"🐍 SWE venv exists: {executor.swe_venv_python.exists()}")
        print(f"🤖 SWE agent exists: {executor.swe_agent_path.exists()}")
        
        # Test config creation
        print("\n📝 Testing config creation...")
        config = SimpleSWEConfig(
            model_name="claude-3-5-sonnet-20241022",
            repo_path="/u/Arun Dev/Python Projects/AI-Coding-Agent",
            problem_statement="Test problem statement",
            execution_timeout=1800,
            total_execution_timeout=0,
            max_consecutive_execution_timeouts=20
        )
        print("✅ Config created successfully")
        
        print("\n🎉 All imports and creations successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import/creation failed: {e}")
        logger.exception("Import failed")
        return False

if __name__ == "__main__":
    success = test_import_in_server_context()
    if success:
        print("\n✅ Import test passed - the issue is elsewhere")
    else:
        print("\n❌ Import test failed - this is the root cause")
