
# 🚀 Quick Start Guide - Improved AI Coding Agent

## 1. Prerequisites
- VS Code installed
- Bridge server running (localhost:8080)
- AI Coding Agent extension compiled

## 2. Launch Extension
1. Open VS Code
2. Press `Ctrl+Shift+P` (Command Palette)
3. Type "AI Coding Agent: Open Execution View"
4. Press Enter

## 3. Start Chatting
1. Type your request in the input box
2. Press Enter or click Send
3. Watch your message appear as a chat bubble
4. AI response appears with collapsible technical sections

## 4. Explore Features
- **Chat Bubbles**: Messages flow naturally without expansion
- **Technical Elements**: Click to expand code, commands, files
- **UI Toggle**: Switch between chat and execution views
- **Real-time Updates**: See progress as AI works
- **Extended Tasks**: 30+ minute AI agent execution

## 5. Example Requests
- "Create a Python web scraper for news articles"
- "Analyze this codebase and suggest improvements"
- "Fix the bug in my authentication system"
- "Implement a REST API with FastAPI"

## 6. Tips
- Use natural language - the AI understands context
- Technical details are automatically organized
- Long tasks run with extended timeouts
- Toggle UI modes if you prefer the old interface

Enjoy your improved AI coding experience! 🎉
