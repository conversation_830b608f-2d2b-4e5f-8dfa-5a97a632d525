#!/usr/bin/env python3
"""
Simple test for SWE-Agent git bypass functionality.
"""

import sys
import os
import time
import logging

# Add project root to path
sys.path.append('/u/Arun Dev/Python Projects/AI-Coding-Agent')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_simple_git_bypass():
    """Test the git bypass with repository copy approach."""
    try:
        from bridge.core.local_swe_executor import get_local_swe_executor, LocalSWEConfig
        
        print("=== Simple Git Bypass Test ===")
        
        # Create executor
        executor = get_local_swe_executor()
        print(f"✅ Local SWE executor created")
        
        # Test with repository copy (this should work)
        config = LocalSWEConfig(
            repo_path="/u/Arun Dev/Python Projects/AI-Coding-Agent",
            problem_statement="List the main files and directories in this project",
            model_name="claude-3-5-sonnet-20241022",
            max_cost=0.05,  # Very low cost limit
            max_calls=3,    # Very few calls
            timeout=30,     # Short timeout
            bypass_git_validation=True,
            use_repo_copy=True
        )
        
        session_id = "simple_git_bypass_test"
        
        print(f"Repository: {config.repo_path}")
        print(f"Bypass enabled: {config.bypass_git_validation}")
        print(f"Use repo copy: {config.use_repo_copy}")
        
        # Check git status
        is_git = executor._is_git_repository(config.repo_path)
        is_dirty = executor._is_git_dirty(config.repo_path) if is_git else False
        
        print(f"Is git repository: {is_git}")
        print(f"Has uncommitted changes: {is_dirty}")
        
        if is_dirty:
            print("✅ Repository has uncommitted changes - perfect for testing bypass")
        
        # Add simple output callback
        output_lines = []
        
        def output_callback(sid, line, stream):
            output_lines.append(f"[{stream}] {line}")
            print(f"📝 {line}")
        
        executor.add_output_callback(session_id, output_callback)
        
        # Start execution
        print("\n=== Starting SWE-Agent with Git Bypass ===")
        success, message = executor.start_agent(session_id, config)
        
        if success:
            print(f"✅ SWE-Agent started: {message}")
            
            # Monitor for a short time
            print("⏳ Monitoring execution...")
            for i in range(20):  # Monitor for 20 seconds
                if not executor.is_running(session_id):
                    print("🏁 Process completed")
                    break
                
                print(f"⏱️  Running... ({i+1}/20 seconds)")
                time.sleep(1)
            
            # Stop if still running
            if executor.is_running(session_id):
                print("⏹️  Stopping process")
                executor.stop_agent(session_id)
            
            print(f"\n=== Execution Results ===")
            print(f"Total output lines: {len(output_lines)}")
            
            if output_lines:
                print("Output summary:")
                for line in output_lines:
                    if any(keyword in line.lower() for keyword in ['error', 'traceback', 'failed']):
                        print(f"❌ {line}")
                    elif any(keyword in line.lower() for keyword in ['success', 'completed', 'finished']):
                        print(f"✅ {line}")
                    else:
                        print(f"ℹ️  {line}")
            
            # Cleanup
            executor.cleanup_session(session_id)
            print("✅ Session cleaned up")
            
            # Determine success based on output
            has_errors = any('error' in line.lower() or 'traceback' in line.lower() for line in output_lines)
            
            if has_errors:
                print("❌ Test completed but with errors")
                return False
            else:
                print("✅ Test completed successfully!")
                return True
            
        else:
            print(f"❌ Failed to start SWE-Agent: {message}")
            return False
            
    except Exception as e:
        logger.exception(f"Error in simple git bypass test: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 Starting Simple Git Bypass Test")
    
    success = test_simple_git_bypass()
    
    print(f"\n=== Final Result ===")
    if success:
        print("🎉 Git bypass functionality is working!")
        print("✅ SWE-Agent can now run on repositories with uncommitted changes")
    else:
        print("⚠️  Git bypass test failed - check output above for details")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
