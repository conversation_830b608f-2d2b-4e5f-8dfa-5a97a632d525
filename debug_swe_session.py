#!/usr/bin/env python3
"""
Debug script to check SWE-Agent session status and output.
"""

import requests
import json
import time
import subprocess
import os

def check_session_status():
    """Check the status of all sessions."""
    try:
        response = requests.get("http://localhost:8080/api/sessions")
        if response.status_code == 200:
            sessions = response.json().get("sessions", [])
            print(f"Found {len(sessions)} sessions:")
            
            for session in sessions:
                print(f"\nSession ID: {session['session_id']}")
                print(f"Status: {session['status']}")
                print(f"Progress: {session['progress']}")
                print(f"Current Step: {session['current_step']}")
                print(f"Created: {session['created_at']}")
                if session.get('error_message'):
                    print(f"Error: {session['error_message']}")
                    
            return sessions
        else:
            print(f"Failed to get sessions: {response.status_code}")
            return []
    except Exception as e:
        print(f"Error checking sessions: {e}")
        return []

def check_docker_containers():
    """Check running Docker containers related to SWE-Agent."""
    try:
        result = subprocess.run(
            ["docker", "ps", "--format", "table {{.ID}}\t{{.Image}}\t{{.Status}}\t{{.Names}}"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("\nRunning Docker containers:")
            print(result.stdout)
        else:
            print(f"Failed to check Docker containers: {result.stderr}")
            
    except Exception as e:
        print(f"Error checking Docker containers: {e}")

def check_swe_processes():
    """Check SWE-Agent related processes."""
    try:
        result = subprocess.run(
            ["ps", "aux"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            swe_lines = [line for line in lines if 'swe' in line.lower() or 'python' in line.lower()]
            
            print("\nSWE-Agent related processes:")
            for line in swe_lines[:10]:  # Limit output
                if 'sweagent' in line or 'swerex' in line:
                    print(line)
                    
    except Exception as e:
        print(f"Error checking processes: {e}")

def check_logs():
    """Check recent log entries."""
    log_files = [
        "logs/swe_agent.log",
        "logs/bridge.log",
        "logs/session.log"
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"\n=== Recent entries from {log_file} ===")
            try:
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    # Show last 5 lines
                    for line in lines[-5:]:
                        print(line.strip())
            except Exception as e:
                print(f"Error reading {log_file}: {e}")
        else:
            print(f"\n{log_file} does not exist")

def test_simple_swe_executor():
    """Test if we can import and use the simple SWE executor."""
    try:
        import sys
        sys.path.append('/u/Arun Dev/Python Projects/AI-Coding-Agent')
        
        from bridge.core.simple_swe_executor import get_simple_swe_executor
        
        executor = get_simple_swe_executor()
        print(f"\nSimple SWE Executor status:")
        print(f"Active processes: {len(executor.active_processes)}")
        print(f"Process threads: {len(executor.process_threads)}")
        
        for session_id, process in executor.active_processes.items():
            print(f"Session {session_id}: Process running = {process.poll() is None}")
            
    except Exception as e:
        print(f"Error testing simple SWE executor: {e}")

def main():
    print("=== SWE-Agent Session Debug Tool ===")
    
    # Check session status
    sessions = check_session_status()
    
    # Check Docker containers
    check_docker_containers()
    
    # Check processes
    check_swe_processes()
    
    # Check logs
    check_logs()
    
    # Test executor
    test_simple_swe_executor()
    
    # Find running sessions
    running_sessions = [s for s in sessions if s['status'] == 'running']
    
    if running_sessions:
        print(f"\n=== Found {len(running_sessions)} running sessions ===")
        for session in running_sessions:
            session_id = session['session_id']
            print(f"\nChecking session {session_id}...")
            
            # Get detailed session info
            try:
                response = requests.get(f"http://localhost:8080/api/sessions/{session_id}")
                if response.status_code == 200:
                    session_detail = response.json()
                    print(f"Detailed status: {json.dumps(session_detail, indent=2)}")
                else:
                    print(f"Failed to get session details: {response.status_code}")
            except Exception as e:
                print(f"Error getting session details: {e}")

if __name__ == "__main__":
    main()
