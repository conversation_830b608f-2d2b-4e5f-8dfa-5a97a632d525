#!/usr/bin/env python3
"""
Test the simple SWE executor with correct configuration.
"""

import time
import logging
from bridge.core.simple_swe_executor import get_simple_swe_executor, SimpleSWEConfig

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_simple_executor():
    """Test the simple SWE executor."""
    print("🧪 Testing Simple SWE Executor")
    print("=" * 40)
    
    try:
        # Get executor
        print("📦 Getting simple SWE executor...")
        executor = get_simple_swe_executor()
        print("✅ Simple SWE executor created successfully")
        
        # Create test configuration
        print("\n📝 Creating test configuration...")
        config = SimpleSWEConfig(
            model_name="claude-3-5-sonnet-20241022",
            repo_path="/u/Arun Dev/Python Projects/AI-Coding-Agent",
            problem_statement="List all Python files in the current directory and show their sizes",
            execution_timeout=1800,
            total_execution_timeout=0,
            max_consecutive_execution_timeouts=20
        )
        print("✅ Configuration created")
        
        # Test session ID
        session_id = "test_session_001"
        
        # Add output callback
        def output_callback(session_id: str, line: str, stream: str):
            print(f"[{stream.upper()}] {line}")
            
        executor.add_output_callback(session_id, output_callback)
        print("✅ Output callback added")
        
        # Start the agent
        print(f"\n🚀 Starting SWE-Agent for session {session_id}...")
        success = executor.start_agent(session_id, config)
        
        if success:
            print("✅ SWE-Agent started successfully!")
            
            # Monitor for a while
            print("\n⏱️  Monitoring execution for 60 seconds...")
            start_time = time.time()
            
            while time.time() - start_time < 60:
                if not executor.is_running(session_id):
                    print("🏁 SWE-Agent execution completed")
                    break
                    
                time.sleep(2)
                print(".", end="", flush=True)
                
            # Stop if still running
            if executor.is_running(session_id):
                print(f"\n⏹️  Stopping SWE-Agent for session {session_id}...")
                executor.stop_agent(session_id)
                
            # Cleanup
            executor.cleanup_session(session_id)
            print("🧹 Session cleaned up")
            
        else:
            print("❌ Failed to start SWE-Agent")
            return False
            
        print("\n🎉 Simple executor test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Test failed")
        return False

if __name__ == "__main__":
    test_simple_executor()
