#!/usr/bin/env python3
"""
Test script for local SWE-Agent execution.
"""

import sys
import os
import time
import logging

# Add project root to path
sys.path.append('/u/Arun Dev/Python Projects/AI-Coding-Agent')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_local_swe_execution():
    """Test local SWE-Agent execution."""
    try:
        from bridge.core.local_swe_executor import get_local_swe_executor, LocalSWEConfig
        
        print("=== Testing Local SWE-Agent Execution ===")
        
        # Create executor
        executor = get_local_swe_executor()
        print(f"✅ Local SWE executor created")
        
        # Create test config
        config = LocalSWEConfig(
            repo_path="/u/Arun Dev/Python Projects/AI-Coding-Agent",
            problem_statement="Analyze this AI Coding Agent project and explain its main components",
            model_name="claude-3-5-sonnet-20241022",
            max_cost=1.0,
            max_calls=20,
            timeout=120  # 2 minutes for testing
        )
        
        print(f"✅ Config created:")
        print(f"   Repository: {config.repo_path}")
        print(f"   Problem: {config.problem_statement}")
        print(f"   Model: {config.model_name}")
        
        # Check prerequisites
        print("\n=== Checking Prerequisites ===")
        
        # Check repository exists
        if os.path.exists(config.repo_path):
            print(f"✅ Repository exists: {config.repo_path}")
        else:
            print(f"❌ Repository not found: {config.repo_path}")
            return False
        
        # Check SWE venv
        if executor.swe_venv_python.exists():
            print(f"✅ SWE venv found: {executor.swe_venv_python}")
        else:
            print(f"❌ SWE venv not found: {executor.swe_venv_python}")
            return False
        
        # Check API keys
        if "ANTHROPIC_API_KEY" in os.environ:
            print("✅ ANTHROPIC_API_KEY found")
        elif "OPENAI_API_KEY" in os.environ:
            print("✅ OPENAI_API_KEY found")
        else:
            print("❌ No API keys found (ANTHROPIC_API_KEY or OPENAI_API_KEY)")
            return False
        
        # Add output callback for monitoring
        session_id = "test_local_session"
        output_lines = []
        
        def output_callback(sid, line, stream):
            output_lines.append(f"[{stream}] {line}")
            print(f"📝 Output: [{stream}] {line}")
        
        executor.add_output_callback(session_id, output_callback)
        
        # Start local execution
        print(f"\n=== Starting Local SWE-Agent ===")
        success, message = executor.start_agent(session_id, config)
        
        if success:
            print(f"✅ Local SWE-Agent started: {message}")
            
            # Monitor for a while
            print("⏳ Monitoring execution...")
            for i in range(60):  # Monitor for 60 seconds
                if not executor.is_running(session_id):
                    print("🏁 Process completed")
                    break
                
                print(f"⏱️  Running... ({i+1}/60 seconds)")
                time.sleep(1)
            
            # Check if still running
            if executor.is_running(session_id):
                print("⏹️  Stopping process (timeout)")
                executor.stop_agent(session_id)
            
            print(f"\n=== Execution Summary ===")
            print(f"Total output lines: {len(output_lines)}")
            if output_lines:
                print("Recent output:")
                for line in output_lines[-5:]:
                    print(f"  {line}")
            
            # Cleanup
            executor.cleanup_session(session_id)
            print("✅ Session cleaned up")
            
            return True
            
        else:
            print(f"❌ Failed to start local SWE-Agent: {message}")
            return False
            
    except Exception as e:
        logger.exception(f"Error testing local SWE execution: {e}")
        return False

def test_swe_agent_command():
    """Test basic SWE-Agent command availability."""
    try:
        import subprocess
        
        print("\n=== Testing SWE-Agent Command ===")
        
        # Test basic command
        result = subprocess.run([
            "/u/Arun Dev/Python Projects/AI-Coding-Agent/swe_venv/bin/python",
            "-m", "sweagent", "--help"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ SWE-Agent command works")
            print("Available commands:")
            for line in result.stdout.split('\n')[:10]:
                if line.strip():
                    print(f"  {line}")
            return True
        else:
            print(f"❌ SWE-Agent command failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing SWE-Agent command: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 Starting Local SWE-Agent Tests")
    
    # Test 1: Basic command
    cmd_test = test_swe_agent_command()
    
    # Test 2: Local execution (only if command works)
    if cmd_test:
        local_test = test_local_swe_execution()
    else:
        print("⏭️  Skipping local execution test due to command failure")
        local_test = False
    
    # Summary
    print(f"\n=== Test Results ===")
    print(f"Command test: {'✅ PASS' if cmd_test else '❌ FAIL'}")
    print(f"Local execution test: {'✅ PASS' if local_test else '❌ FAIL'}")
    
    if cmd_test and local_test:
        print("🎉 All tests passed! Local SWE-Agent is ready.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
