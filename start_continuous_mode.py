#!/usr/bin/env python3
"""
Startup script for AI Coding Agent in continuous execution mode.
This script initializes the bridge server with continuous execution capabilities.
"""

import os
import sys
import time
import signal
import logging
import threading
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from bridge.core.enhanced_logging import setup_logging
from bridge.core.continuous_execution_manager import continuous_manager
from bridge.core.session_manager import session_manager
from bridge.api.api_server import run_server

# Setup logging
logger = setup_logging("continuous_mode")


class ContinuousAgentServer:
    """Server for continuous AI Coding Agent execution."""
    
    def __init__(self):
        self.server_thread = None
        self.is_running = False
        
    def start(self):
        """Start the continuous agent server."""
        logger.info("Starting AI Coding Agent in Continuous Mode")
        logger.info("=" * 60)
        
        # Initialize continuous execution manager
        continuous_manager.session_manager = session_manager
        continuous_manager.start()
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # Start the API server in a separate thread
        self.server_thread = threading.Thread(
            target=run_server,
            kwargs={"debug": False},
            daemon=True
        )
        self.server_thread.start()
        
        self.is_running = True
        logger.info("Continuous Agent Server started successfully")
        logger.info("API Server: http://localhost:8080")
        logger.info("WebSocket: ws://localhost:8080")
        logger.info("Continuous execution manager is running")
        logger.info("Ready to accept continuous tasks...")
        
        # Keep the main thread alive
        try:
            while self.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            self._shutdown()
            
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, shutting down...")
        self._shutdown()
        
    def _shutdown(self):
        """Shutdown the server gracefully."""
        if not self.is_running:
            return
            
        logger.info("Shutting down Continuous Agent Server...")
        self.is_running = False
        
        # Stop continuous execution manager
        continuous_manager.stop()
        
        # Wait for server thread to finish
        if self.server_thread and self.server_thread.is_alive():
            self.server_thread.join(timeout=5)
            
        logger.info("Continuous Agent Server stopped")
        sys.exit(0)


def print_usage():
    """Print usage information."""
    print("""
AI Coding Agent - Continuous Execution Mode

This mode enables the agent to run continuously without timeout limitations,
perfect for complex, long-running coding tasks.

Features:
- Unlimited execution time
- Automatic error recovery
- Parallel sub-task support
- Session persistence
- Real-time progress monitoring

Usage:
    python start_continuous_mode.py

Environment Variables:
    ANTHROPIC_API_KEY    - Required for Claude models
    OPENAI_API_KEY       - Optional for GPT models
    BRIDGE_API_PORT      - API server port (default: 8080)
    LOG_LEVEL           - Logging level (default: INFO)

API Endpoints:
    POST /api/sessions/continuous     - Create continuous session
    POST /api/sessions/continuous/<id>/subtask - Add sub-task
    GET  /api/sessions/continuous/tasks - List all tasks
    
WebSocket Events:
    - session_event: Real-time session updates
    - trajectory_updated: Agent progress updates
    - task_progress: Continuous task progress

Examples:
    # Create a continuous session
    curl -X POST http://localhost:8080/api/sessions/continuous \\
         -H "Content-Type: application/json" \\
         -d '{
           "problem_statement": "Refactor the entire codebase for better performance",
           "repo_path": "/path/to/repo",
           "model_name": "claude-sonnet-4-20250514",
           "enable_parallel": true
         }'
    
    # Add a sub-task
    curl -X POST http://localhost:8080/api/sessions/continuous/<session_id>/subtask \\
         -H "Content-Type: application/json" \\
         -d '{
           "description": "Optimize database queries",
           "priority": 2
         }'

For VS Code integration, use the AI Coding Agent extension with continuous mode enabled.
""")


def check_requirements():
    """Check if all requirements are met."""
    errors = []
    
    # Check API keys
    if not os.getenv('ANTHROPIC_API_KEY') and not os.getenv('OPENAI_API_KEY'):
        errors.append("No API keys found. Set ANTHROPIC_API_KEY or OPENAI_API_KEY")
    
    # Check Python version
    if sys.version_info < (3, 8):
        errors.append("Python 3.8 or higher is required")
    
    # Check required directories
    required_dirs = ['bridge', 'swe-agent', 'logs']
    for dir_name in required_dirs:
        if not (project_root / dir_name).exists():
            errors.append(f"Required directory '{dir_name}' not found")
    
    if errors:
        logger.error("Requirements check failed:")
        for error in errors:
            logger.error(f"  - {error}")
        return False
    
    return True


def main():
    """Main entry point."""
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        print_usage()
        return
    
    print("AI Coding Agent - Continuous Execution Mode")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Create logs directory if it doesn't exist
    logs_dir = project_root / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    # Start the server
    server = ContinuousAgentServer()
    server.start()


if __name__ == "__main__":
    main()
