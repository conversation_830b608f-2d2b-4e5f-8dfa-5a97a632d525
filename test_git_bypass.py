#!/usr/bin/env python3
"""
Test script for SWE-Agent git repository bypass functionality.
This script tests the ability to run SWE-Agent on repositories with uncommitted changes.
"""

import sys
import os
import time
import logging
import tempfile
import subprocess

# Add project root to path
sys.path.append('/u/Arun Dev/Python Projects/AI-Coding-Agent')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_git_bypass_functionality():
    """Test the git bypass functionality with different scenarios."""
    try:
        from bridge.core.local_swe_executor import get_local_swe_executor, LocalSWEConfig
        
        print("=== Testing SWE-Agent Git Repository Bypass ===")
        
        # Create executor
        executor = get_local_swe_executor()
        print(f"✅ Local SWE executor created")
        
        # Test repository path
        repo_path = "/u/Arun Dev/Python Projects/AI-Coding-Agent"
        
        # Check git status
        print(f"\n=== Repository Status Check ===")
        print(f"Repository: {repo_path}")
        
        # Check if it's a git repository
        is_git = executor._is_git_repository(repo_path)
        print(f"Is git repository: {is_git}")
        
        if is_git:
            is_dirty = executor._is_git_dirty(repo_path)
            print(f"Has uncommitted changes: {is_dirty}")
            
            if is_dirty:
                print("✅ Perfect! Repository has uncommitted changes - ideal for testing bypass")
            else:
                print("⚠️  Repository is clean - creating test changes for bypass testing")
                # Create a test file to make repository dirty
                test_file = os.path.join(repo_path, "test_git_bypass_temp.txt")
                with open(test_file, 'w') as f:
                    f.write("Temporary file for testing git bypass functionality\n")
                print(f"Created temporary file: {test_file}")
        
        # Test scenarios
        scenarios = [
            {
                "name": "Bypass with Repository Copy",
                "config": LocalSWEConfig(
                    repo_path=repo_path,
                    problem_statement="Test git bypass with repository copy - list main files",
                    model_name="claude-3-5-sonnet-20241022",
                    max_cost=0.1,
                    max_calls=5,
                    timeout=60,
                    bypass_git_validation=True,
                    use_repo_copy=True
                )
            },
            {
                "name": "Bypass without Repository Copy",
                "config": LocalSWEConfig(
                    repo_path=repo_path,
                    problem_statement="Test git bypass without repository copy - list main files",
                    model_name="claude-3-5-sonnet-20241022",
                    max_cost=0.1,
                    max_calls=5,
                    timeout=60,
                    bypass_git_validation=True,
                    use_repo_copy=False
                )
            }
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n=== Test Scenario {i}: {scenario['name']} ===")
            
            session_id = f"test_git_bypass_{i}"
            config = scenario['config']
            
            print(f"Session ID: {session_id}")
            print(f"Bypass enabled: {config.bypass_git_validation}")
            print(f"Use repo copy: {config.use_repo_copy}")
            
            # Add output callback for monitoring
            output_lines = []
            
            def output_callback(sid, line, stream):
                output_lines.append(f"[{stream}] {line}")
                print(f"📝 [{stream}] {line}")
            
            executor.add_output_callback(session_id, output_callback)
            
            # Test repository copy creation if enabled
            if config.use_repo_copy:
                try:
                    print("Testing repository copy creation...")
                    clean_repo_path = executor._create_clean_repo_copy(repo_path, session_id)
                    print(f"✅ Successfully created clean repository copy: {clean_repo_path}")
                    
                    # Verify the copy
                    if os.path.exists(clean_repo_path):
                        files = os.listdir(clean_repo_path)
                        print(f"Copy contains {len(files)} items")
                        
                        # Check if it's a git repository
                        if executor._is_git_repository(clean_repo_path):
                            is_clean = not executor._is_git_dirty(clean_repo_path)
                            print(f"Copy is clean git repository: {is_clean}")
                        
                    # Cleanup the test copy
                    executor._cleanup_repo_copy(session_id)
                    print("✅ Repository copy cleaned up")
                    
                except Exception as e:
                    print(f"❌ Repository copy test failed: {e}")
                    continue
            
            # Test actual SWE-Agent execution
            print("Starting SWE-Agent execution...")
            success, message = executor.start_agent(session_id, config)
            
            if success:
                print(f"✅ SWE-Agent started: {message}")
                
                # Monitor for a short time
                print("⏳ Monitoring execution...")
                for j in range(30):  # Monitor for 30 seconds
                    if not executor.is_running(session_id):
                        print("🏁 Process completed")
                        break
                    
                    print(f"⏱️  Running... ({j+1}/30 seconds)")
                    time.sleep(1)
                
                # Check if still running
                if executor.is_running(session_id):
                    print("⏹️  Stopping process (timeout)")
                    executor.stop_agent(session_id)
                
                print(f"\n=== Execution Summary for {scenario['name']} ===")
                print(f"Total output lines: {len(output_lines)}")
                if output_lines:
                    print("Recent output:")
                    for line in output_lines[-3:]:
                        print(f"  {line}")
                
                # Cleanup
                executor.cleanup_session(session_id)
                print("✅ Session cleaned up")
                
                print(f"✅ Test scenario '{scenario['name']}' completed successfully")
                
            else:
                print(f"❌ Failed to start SWE-Agent: {message}")
                print(f"❌ Test scenario '{scenario['name']}' failed")
        
        # Cleanup test file if created
        test_file = os.path.join(repo_path, "test_git_bypass_temp.txt")
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"🧹 Cleaned up test file: {test_file}")
        
        print("\n🎉 Git bypass functionality testing completed!")
        return True
        
    except Exception as e:
        logger.exception(f"Error testing git bypass functionality: {e}")
        return False

def test_git_utilities():
    """Test the git utility functions."""
    try:
        from bridge.core.local_swe_executor import get_local_swe_executor
        
        print("\n=== Testing Git Utility Functions ===")
        
        executor = get_local_swe_executor()
        repo_path = "/u/Arun Dev/Python Projects/AI-Coding-Agent"
        
        # Test git repository detection
        is_git = executor._is_git_repository(repo_path)
        print(f"✅ Git repository detection: {is_git}")
        
        if is_git:
            # Test dirty status detection
            is_dirty = executor._is_git_dirty(repo_path)
            print(f"✅ Git dirty status detection: {is_dirty}")
            
            # Show git status for reference
            try:
                result = subprocess.run(
                    ["git", "status", "--porcelain"],
                    cwd=repo_path,
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    status_lines = result.stdout.strip().split('\n') if result.stdout.strip() else []
                    print(f"Git status shows {len(status_lines)} changed files")
                    if status_lines and status_lines[0]:
                        print("Sample changes:")
                        for line in status_lines[:3]:
                            print(f"  {line}")
            except Exception as e:
                print(f"Could not get git status: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing git utilities: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 Starting SWE-Agent Git Bypass Tests")
    
    # Test 1: Git utility functions
    utils_test = test_git_utilities()
    
    # Test 2: Git bypass functionality
    if utils_test:
        bypass_test = test_git_bypass_functionality()
    else:
        print("⏭️  Skipping bypass test due to utility test failure")
        bypass_test = False
    
    # Summary
    print(f"\n=== Test Results ===")
    print(f"Git utilities test: {'✅ PASS' if utils_test else '❌ FAIL'}")
    print(f"Git bypass test: {'✅ PASS' if bypass_test else '❌ FAIL'}")
    
    if utils_test and bypass_test:
        print("🎉 All tests passed! Git bypass functionality is working.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
