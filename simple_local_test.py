#!/usr/bin/env python3
"""
Simple test for local SWE-Agent execution.
"""

import subprocess
import tempfile
import os
import yaml

def test_local_swe_agent():
    """Test local SWE-Agent execution with minimal config."""
    
    # Create a minimal config for local execution
    config = {
        "agent": {
            "model": {
                "name": "claude-3-5-sonnet-20241022",
                "per_instance_cost_limit": 0.5,
                "per_instance_call_limit": 10,
            },
            "tools": {
                "bundles": [{"path": "tools/search"}],
                "enable_bash_tool": True,
                "parse_function": {"type": "function_calling"},
                "execution_timeout": 10,
            },
            "templates": {
                "system_template": "You are a helpful assistant.",
                "instance_template": "List the files in the current directory using bash commands.",
            }
        },
        "env": {
            "deployment": {"type": "local"},
            "repo": {
                "type": "local",
                "path": "/u/Arun Dev/Python Projects/AI-Coding-Agent"
            }
        },
        "problem_statement": {
            "text": "List the files in this directory"
        }
    }
    
    # Write config to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        yaml.dump(config, f, default_flow_style=False)
        config_file = f.name
    
    try:
        print("Testing local SWE-Agent execution...")
        print(f"Config file: {config_file}")
        
        # Check if we have API keys
        if "ANTHROPIC_API_KEY" not in os.environ and "OPENAI_API_KEY" not in os.environ:
            print("❌ No API keys found. Please set ANTHROPIC_API_KEY or OPENAI_API_KEY.")
            return False
        
        # Test command
        cmd = [
            "swe_venv/bin/python",
            "-m", "sweagent", "run",
            "--config", config_file,
            "--env.deployment.type", "local",
            "--env.repo.path", "/u/Arun Dev/Python Projects/AI-Coding-Agent",
            "--problem_statement.text", "List the files in this directory",
            "--output_dir", "/tmp/swe_test_local",
            "--agent.model.per_instance_cost_limit", "0.1",
            "--agent.model.per_instance_call_limit", "5"
        ]
        
        print(f"Command: {' '.join(cmd)}")
        
        # Run with timeout
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60,  # 1 minute timeout
                cwd="/u/Arun Dev/Python Projects/AI-Coding-Agent"
            )
            
            print(f"Return code: {result.returncode}")
            
            if result.stdout:
                print("STDOUT:")
                print(result.stdout[:1000])  # First 1000 chars
            
            if result.stderr:
                print("STDERR:")
                print(result.stderr[:1000])  # First 1000 chars
            
            if result.returncode == 0:
                print("✅ Local SWE-Agent execution successful!")
                return True
            else:
                print(f"❌ Local SWE-Agent execution failed with code {result.returncode}")
                return False
                
        except subprocess.TimeoutExpired:
            print("⏰ Local SWE-Agent execution timed out")
            return False
            
    except Exception as e:
        print(f"❌ Error testing local SWE-Agent: {e}")
        return False
    finally:
        # Cleanup
        try:
            os.unlink(config_file)
        except:
            pass

if __name__ == "__main__":
    success = test_local_swe_agent()
    print(f"\nTest result: {'✅ PASS' if success else '❌ FAIL'}")
