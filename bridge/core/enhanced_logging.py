"""
Enhanced logging system for the AI Coding Agent bridge.
Provides structured, component-based logging with rotation and filtering.
"""

import logging
import logging.handlers
import json
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum

from bridge.core.config import config, PROJECT_ROOT


class LogLevel(Enum):
    """Log levels with numeric values."""
    TRACE = 5
    DEBUG = 10
    INFO = 20
    WARNING = 30
    ERROR = 40
    CRITICAL = 50


class LogComponent(Enum):
    """Log components for filtering and organization."""
    API = "api"
    SWE_AGENT = "swe_agent"
    CHAT = "chat"
    SESSION = "session"
    AUTH = "auth"
    WORKSPACE = "workspace"
    TERMINAL = "terminal"
    SECURITY = "security"
    PERFORMANCE = "performance"
    AUDIT = "audit"


@dataclass
class LogEntry:
    """Structured log entry."""
    timestamp: str
    level: str
    component: str
    module: str
    message: str
    context: Dict[str, Any]
    request_id: Optional[str] = None
    session_id: Optional[str] = None
    user_id: Optional[str] = None


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured JSON logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON."""
        # Extract component from logger name
        component = getattr(record, 'component', 'unknown')
        if not component or component == 'unknown':
            # Try to extract from logger name
            name_parts = record.name.split('.')
            if len(name_parts) >= 2 and name_parts[1] in [c.value for c in LogComponent]:
                component = name_parts[1]
            else:
                component = 'general'
        
        # Create structured log entry
        log_entry = LogEntry(
            timestamp=datetime.fromtimestamp(record.created).isoformat(),
            level=record.levelname,
            component=component,
            module=record.name,
            message=record.getMessage(),
            context=getattr(record, 'context', {}),
            request_id=getattr(record, 'request_id', None),
            session_id=getattr(record, 'session_id', None),
            user_id=getattr(record, 'user_id', None)
        )
        
        return json.dumps(asdict(log_entry), ensure_ascii=False)


class ComponentFilter(logging.Filter):
    """Filter logs by component."""
    
    def __init__(self, component: Union[LogComponent, str]):
        super().__init__()
        self.component = component.value if isinstance(component, LogComponent) else component
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter record by component."""
        record_component = getattr(record, 'component', 'unknown')
        if not record_component or record_component == 'unknown':
            # Try to extract from logger name
            name_parts = record.name.split('.')
            if len(name_parts) >= 2:
                record_component = name_parts[1]
        
        return record_component == self.component


class EnhancedLogger:
    """Enhanced logger with component-based filtering and structured logging."""
    
    def __init__(self, name: str, component: Optional[LogComponent] = None):
        self.name = name
        self.component = component
        self.logger = logging.getLogger(name)
        self._setup_logger()
    
    def _setup_logger(self):
        """Set up logger with appropriate handlers."""
        if self.logger.handlers:
            return  # Already set up
        
        # Set logger level to lowest to let handlers control filtering
        self.logger.setLevel(logging.DEBUG)
        self.logger.propagate = False
        
        # Add component to logger if specified
        if self.component:
            self.logger.component = self.component.value
    
    def _log_with_context(self, level: int, message: str, context: Optional[Dict[str, Any]] = None,
                         request_id: Optional[str] = None, session_id: Optional[str] = None,
                         user_id: Optional[str] = None, **kwargs):
        """Log message with context."""
        extra = {
            'context': context or {},
            'request_id': request_id,
            'session_id': session_id,
            'user_id': user_id
        }
        
        if self.component:
            extra['component'] = self.component.value
        
        # Add any additional kwargs to context
        if kwargs:
            extra['context'].update(kwargs)
        
        self.logger.log(level, message, extra=extra)
    
    def trace(self, message: str, **kwargs):
        """Log trace message."""
        self._log_with_context(LogLevel.TRACE.value, message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        self._log_with_context(LogLevel.DEBUG.value, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message."""
        self._log_with_context(LogLevel.INFO.value, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message."""
        self._log_with_context(LogLevel.WARNING.value, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message."""
        self._log_with_context(LogLevel.ERROR.value, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message."""
        self._log_with_context(LogLevel.CRITICAL.value, message, **kwargs)
    
    def exception(self, message: str, **kwargs):
        """Log exception with traceback."""
        extra = {
            'context': kwargs.get('context', {}),
            'request_id': kwargs.get('request_id'),
            'session_id': kwargs.get('session_id'),
            'user_id': kwargs.get('user_id')
        }
        
        if self.component:
            extra['component'] = self.component.value
        
        self.logger.exception(message, extra=extra)


class LoggingManager:
    """Central logging manager for the bridge system."""
    
    def __init__(self):
        self.logs_dir = Path(config.get("logging", "file", default="logs/bridge.log")).parent
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        self.handlers: Dict[str, logging.Handler] = {}
        self.loggers: Dict[str, EnhancedLogger] = {}
        self._setup_handlers()
    
    def _setup_handlers(self):
        """Set up logging handlers for different components and formats."""
        # Console handler with simple format
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, config.get("logging", "level", default="INFO")))
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        self.handlers['console'] = console_handler
        
        # Main bridge log file with rotation
        main_log_file = self.logs_dir / "bridge.log"
        main_handler = logging.handlers.RotatingFileHandler(
            main_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        main_handler.setLevel(logging.DEBUG)
        main_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        main_handler.setFormatter(main_formatter)
        self.handlers['main'] = main_handler
        
        # Structured JSON log file
        json_log_file = self.logs_dir / "bridge_structured.json"
        json_handler = logging.handlers.RotatingFileHandler(
            json_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        json_handler.setLevel(logging.DEBUG)
        json_handler.setFormatter(StructuredFormatter())
        self.handlers['json'] = json_handler
        
        # Component-specific log files
        for component in LogComponent:
            component_log_file = self.logs_dir / f"{component.value}.log"
            component_handler = logging.handlers.RotatingFileHandler(
                component_log_file,
                maxBytes=5*1024*1024,  # 5MB
                backupCount=3,
                encoding='utf-8'
            )
            component_handler.setLevel(logging.DEBUG)
            component_handler.addFilter(ComponentFilter(component))
            component_handler.setFormatter(main_formatter)
            self.handlers[f'component_{component.value}'] = component_handler
        
        # Error-only log file
        error_log_file = self.logs_dir / "errors.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=10,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(main_formatter)
        self.handlers['error'] = error_handler
        
        # Performance log file
        perf_log_file = self.logs_dir / "performance.log"
        perf_handler = logging.handlers.RotatingFileHandler(
            perf_log_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        perf_handler.setLevel(logging.INFO)
        perf_handler.addFilter(ComponentFilter(LogComponent.PERFORMANCE))
        perf_handler.setFormatter(main_formatter)
        self.handlers['performance'] = perf_handler
        
        # Audit log file
        audit_log_file = self.logs_dir / "audit.log"
        audit_handler = logging.handlers.RotatingFileHandler(
            audit_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        audit_handler.setLevel(logging.INFO)
        audit_handler.addFilter(ComponentFilter(LogComponent.AUDIT))
        audit_handler.setFormatter(main_formatter)
        self.handlers['audit'] = audit_handler

    def get_logger(self, name: str, component: Optional[LogComponent] = None) -> EnhancedLogger:
        """Get or create an enhanced logger."""
        logger_key = f"{name}:{component.value if component else 'general'}"

        if logger_key not in self.loggers:
            logger = EnhancedLogger(name, component)

            # Add all relevant handlers
            logger.logger.addHandler(self.handlers['console'])
            logger.logger.addHandler(self.handlers['main'])
            logger.logger.addHandler(self.handlers['json'])
            logger.logger.addHandler(self.handlers['error'])

            # Add component-specific handler if applicable
            if component:
                component_handler_key = f'component_{component.value}'
                if component_handler_key in self.handlers:
                    logger.logger.addHandler(self.handlers[component_handler_key])

                # Add special handlers for specific components
                if component == LogComponent.PERFORMANCE:
                    logger.logger.addHandler(self.handlers['performance'])
                elif component == LogComponent.AUDIT:
                    logger.logger.addHandler(self.handlers['audit'])

            self.loggers[logger_key] = logger

        return self.loggers[logger_key]

    def clear_logs(self):
        """Clear all log files (useful for testing)."""
        for log_file in self.logs_dir.glob("*.log"):
            try:
                log_file.unlink()
            except OSError:
                pass  # File might be in use

        for log_file in self.logs_dir.glob("*.json"):
            try:
                log_file.unlink()
            except OSError:
                pass  # File might be in use


# Global logging manager instance
logging_manager = LoggingManager()


def get_logger(name: str, component: Optional[LogComponent] = None) -> EnhancedLogger:
    """Get an enhanced logger instance."""
    return logging_manager.get_logger(name, component)


# Convenience functions for common components
def get_api_logger(name: str) -> EnhancedLogger:
    """Get API component logger."""
    return get_logger(name, LogComponent.API)


def get_swe_logger(name: str) -> EnhancedLogger:
    """Get SWE-Agent component logger."""
    return get_logger(name, LogComponent.SWE_AGENT)


def get_chat_logger(name: str) -> EnhancedLogger:
    """Get Chat component logger."""
    return get_logger(name, LogComponent.CHAT)


def get_session_logger(name: str) -> EnhancedLogger:
    """Get Session component logger."""
    return get_logger(name, LogComponent.SESSION)


def get_auth_logger(name: str) -> EnhancedLogger:
    """Get Auth component logger."""
    return get_logger(name, LogComponent.AUTH)


def get_workspace_logger(name: str) -> EnhancedLogger:
    """Get Workspace component logger."""
    return get_logger(name, LogComponent.WORKSPACE)


def get_terminal_logger(name: str) -> EnhancedLogger:
    """Get Terminal component logger."""
    return get_logger(name, LogComponent.TERMINAL)


def get_security_logger(name: str) -> EnhancedLogger:
    """Get Security component logger."""
    return get_logger(name, LogComponent.SECURITY)


def get_performance_logger(name: str) -> EnhancedLogger:
    """Get Performance component logger."""
    return get_logger(name, LogComponent.PERFORMANCE)


def get_audit_logger(name: str) -> EnhancedLogger:
    """Get Audit component logger."""
    return get_logger(name, LogComponent.AUDIT)
