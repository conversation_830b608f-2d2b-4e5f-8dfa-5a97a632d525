"""
Enhanced configuration module for the AI Coding Agent bridge.
Supports all SWE-Agent configuration options and advanced features.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field, asdict

from bridge.utils.env_loader import get_env

# Base paths
PROJECT_ROOT = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
SWE_AGENT_PATH = PROJECT_ROOT / "swe-agent"


@dataclass
class ToolConfig:
    """Configuration for SWE-Agent tools."""
    bundles: List[str] = field(default_factory=lambda: ["registry", "windowed", "search"])
    env_variables: Dict[str, Any] = field(default_factory=dict)
    parse_function: Dict[str, str] = field(default_factory=lambda: {"type": "thought_action"})
    custom_tools: List[str] = field(default_factory=list)


@dataclass
class DeploymentConfig:
    """Configuration for SWE-Agent deployment."""
    type: str = "local"  # local, docker, modal, aws
    image: str = "python:3.11"
    timeout: int = 3600
    memory_limit: Optional[str] = None
    cpu_limit: Optional[str] = None
    gpu_enabled: bool = False
    custom_dockerfile: Optional[str] = None


@dataclass
class RetryConfig:
    """Configuration for retry mechanisms."""
    max_attempts: int = 3
    backoff_factor: float = 2.0
    max_delay: int = 300
    retry_on_errors: List[str] = field(default_factory=lambda: ["timeout", "connection_error"])


@dataclass
class ModelConfig:
    """Configuration for AI models."""
    model_name: str = "claude-sonnet-4-20250514"
    temperature: float = 0.0
    max_tokens: Optional[int] = None
    api_base: Optional[str] = None
    api_version: Optional[str] = None
    custom_headers: Dict[str, str] = field(default_factory=dict)


@dataclass
class RepositoryConfig:
    """Configuration for repository handling."""
    type: str = "auto"  # auto, local, github, preexisting
    path: Optional[str] = None
    url: Optional[str] = None
    base_commit: str = "HEAD"
    branch: Optional[str] = None
    clone_timeout: int = 300
    setup_commands: List[str] = field(default_factory=list)


@dataclass
class AgentConfig:
    """Configuration for SWE-Agent behavior."""
    model: ModelConfig = field(default_factory=ModelConfig)
    tools: ToolConfig = field(default_factory=ToolConfig)
    max_iterations: int = 50
    max_cost: Optional[float] = None
    templates: Optional[str] = None
    history_processors: List[str] = field(default_factory=list)


@dataclass
class EnvironmentConfig:
    """Configuration for execution environment."""
    deployment: DeploymentConfig = field(default_factory=DeploymentConfig)
    repository: RepositoryConfig = field(default_factory=RepositoryConfig)
    env_variables: Dict[str, str] = field(default_factory=dict)
    working_directory: Optional[str] = None


@dataclass
class LoggingConfig:
    """Configuration for logging."""
    level: str = "INFO"
    file: str = str(PROJECT_ROOT / "logs" / "bridge.log")
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    max_size: str = "10MB"
    backup_count: int = 5


@dataclass
class BridgeConfig:
    """Configuration for bridge server."""
    api_host: str = "localhost"
    api_port: int = 8080
    vim_port: int = 8081
    max_sessions: int = 10
    session_timeout: int = 3600
    enable_websockets: bool = True


@dataclass
class SecurityConfig:
    """Configuration for security features."""
    enable_auth: bool = False
    api_key_required: bool = False
    allowed_origins: List[str] = field(default_factory=lambda: ["*"])
    rate_limit: Optional[str] = None
    jwt_secret: Optional[str] = None


@dataclass
class EnhancedConfig:
    """Enhanced configuration for the AI Coding Agent bridge."""
    agent: AgentConfig = field(default_factory=AgentConfig)
    environment: EnvironmentConfig = field(default_factory=EnvironmentConfig)
    bridge: BridgeConfig = field(default_factory=BridgeConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    retry: RetryConfig = field(default_factory=RetryConfig)
    api_keys: Dict[str, str] = field(default_factory=dict)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EnhancedConfig':
        """Create configuration from dictionary."""
        # Handle nested configurations
        agent_data = data.get('agent', {})
        if 'model' in agent_data:
            agent_data['model'] = ModelConfig(**agent_data['model'])
        if 'tools' in agent_data:
            agent_data['tools'] = ToolConfig(**agent_data['tools'])
        
        environment_data = data.get('environment', {})
        if 'deployment' in environment_data:
            environment_data['deployment'] = DeploymentConfig(**environment_data['deployment'])
        if 'repository' in environment_data:
            environment_data['repository'] = RepositoryConfig(**environment_data['repository'])
        
        return cls(
            agent=AgentConfig(**agent_data),
            environment=EnvironmentConfig(**environment_data),
            bridge=BridgeConfig(**data.get('bridge', {})),
            logging=LoggingConfig(**data.get('logging', {})),
            security=SecurityConfig(**data.get('security', {})),
            retry=RetryConfig(**data.get('retry', {})),
            api_keys=data.get('api_keys', {})
        )
    
    @classmethod
    def from_file(cls, config_path: Union[str, Path]) -> 'EnhancedConfig':
        """Load configuration from file."""
        config_path = Path(config_path)
        
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(config_path, 'r') as f:
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                data = yaml.safe_load(f)
            elif config_path.suffix.lower() == '.json':
                import json
                data = json.load(f)
            else:
                raise ValueError(f"Unsupported configuration file format: {config_path.suffix}")
        
        return cls.from_dict(data)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return asdict(self)
    
    def to_swe_agent_config(self) -> Dict[str, Any]:
        """Convert to SWE-Agent compatible configuration."""
        return {
            "agent": {
                "model": asdict(self.agent.model),
                "tools": {
                    "bundles": [{"path": bundle} for bundle in self.agent.tools.bundles],
                    "env_variables": self.agent.tools.env_variables,
                    "parse_function": self.agent.tools.parse_function,
                },
                "max_iterations": self.agent.max_iterations,
                "max_cost": self.agent.max_cost,
            },
            "environment": {
                "deployment": asdict(self.environment.deployment),
                "repo": asdict(self.environment.repository),
            },
        }
    
    def get(self, *keys: str, default: Any = None) -> Any:
        """
        Get a configuration value using dot notation.
        
        Args:
            *keys: Keys to navigate the nested configuration.
            default: Default value if the key is not found.
            
        Returns:
            Configuration value.
        """
        current = asdict(self)
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default
        return current
    
    def update_from_env(self):
        """Update configuration from environment variables."""
        # API keys
        self.api_keys["anthropic"] = get_env("ANTHROPIC_API_KEY", self.api_keys.get("anthropic", ""))
        self.api_keys["openai"] = get_env("OPENAI_API_KEY", self.api_keys.get("openai", ""))
        
        # Bridge configuration
        self.bridge.api_port = int(get_env("BRIDGE_API_PORT", str(self.bridge.api_port)))
        self.bridge.vim_port = int(get_env("BRIDGE_VIM_PORT", str(self.bridge.vim_port)))
        
        # Agent configuration
        self.agent.model.model_name = get_env("SWE_AGENT_MODEL", self.agent.model.model_name)
        
        # Logging
        self.logging.level = get_env("LOG_LEVEL", self.logging.level)


def create_default_config() -> EnhancedConfig:
    """Create default configuration with environment variable overrides."""
    config = EnhancedConfig()
    config.update_from_env()
    return config


def load_config(config_path: Optional[Union[str, Path]] = None) -> EnhancedConfig:
    """
    Load configuration from file or create default.
    
    Args:
        config_path: Optional path to configuration file.
        
    Returns:
        Enhanced configuration object.
    """
    if config_path:
        config = EnhancedConfig.from_file(config_path)
    else:
        config = create_default_config()
    
    # Always update from environment variables
    config.update_from_env()
    
    return config


# Example configuration templates
EXAMPLE_CONFIGS = {
    "basic": {
        "agent": {
            "model": {
                "model_name": "claude-sonnet-4-20250514",
                "temperature": 0.0
            },
            "tools": {
                "bundles": ["registry", "windowed", "search"],
                "env_variables": {
                    "WINDOW": 100,
                    "OVERLAP": 2
                }
            }
        },
        "environment": {
            "deployment": {
                "type": "local",
                "image": "python:3.11"
            }
        }
    },
    
    "advanced": {
        "agent": {
            "model": {
                "model_name": "claude-sonnet-4-20250514",
                "temperature": 0.1,
                "max_tokens": 4000
            },
            "tools": {
                "bundles": ["registry", "windowed", "search", "windowed_edit_linting", "submit"],
                "env_variables": {
                    "WINDOW": 200,
                    "OVERLAP": 5
                }
            },
            "max_iterations": 100
        },
        "environment": {
            "deployment": {
                "type": "docker",
                "image": "python:3.11",
                "timeout": 7200,
                "memory_limit": "4GB"
            }
        },
        "retry": {
            "max_attempts": 5,
            "backoff_factor": 1.5
        },
        "security": {
            "enable_auth": True,
            "api_key_required": True
        }
    },

    "continuous": {
        "agent": {
            "model": {
                "model_name": "claude-sonnet-4-20250514",
                "temperature": 0.0,
                "fallback_models": ["claude-3-opus-20240229", "gpt-4"]
            },
            "tools": {
                "bundles": ["registry", "windowed", "search", "windowed_edit_linting", "submit"],
                "env_variables": {
                    "WINDOW": 200,
                    "OVERLAP": 5
                },
                "execution_timeout": 600,  # 10 minutes per command
                "total_execution_timeout": 0,  # Unlimited total execution time
                "max_consecutive_execution_timeouts": 10,  # More tolerance for timeouts
                "install_timeout": 600
            },
            "max_iterations": 0,  # Unlimited iterations
            "max_cost": 50.0,  # Higher cost limit for long-running tasks
            "continuous_mode": True,
            "auto_resume": True,
            "heartbeat_interval": 300  # 5 minutes
        },
        "environment": {
            "deployment": {
                "type": "docker",
                "image": "python:3.11",
                "memory_limit": "8GB",
                "timeout": 0  # Unlimited deployment timeout
            }
        },
        "repository": {
            "type": "github",
            "clone_timeout": 1200
        },
        "retry": {
            "max_attempts": 10,
            "backoff_factor": 1.2,
            "circuit_breaker_enabled": True,
            "circuit_breaker_threshold": 10
        },
        "session": {
            "persistence_enabled": True,
            "auto_save_interval": 600,  # Save every 10 minutes
            "max_session_duration": 0,  # Unlimited
            "cleanup_on_exit": False
        },
        "parallel": {
            "enabled": True,
            "max_workers": 4,
            "task_queue_size": 100
        },
        "security": {
            "enable_auth": True,
            "api_key_required": True
        },
        "enable_metrics": True,
        "enable_tracing": True,
        "enable_debug_logging": True
    }
}


# Global configuration instance
enhanced_config = create_default_config()
