"""
Simplified SWE-Agent Executor for the AI Coding Agent bridge.
Handles actual SWE-Agent process execution without complex imports.
"""

import os
import sys
import json
import time
import threading
import subprocess
import tempfile
import logging
import signal
import psutil
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class SimpleSWEConfig:
    """Simple configuration for SWE-Agent execution."""
    model_name: str
    repo_path: str
    problem_statement: str
    execution_timeout: int = 1800  # 30 minutes
    total_execution_timeout: int = 3600  # 1 hour total timeout
    max_consecutive_execution_timeouts: int = 20
    docker_build_timeout: int = 600  # 10 minutes for Docker build
    startup_timeout: int = 300  # 5 minutes for startup
    enable_git_validation: bool = True
    auto_commit_changes: bool = False


class SimpleSWEExecutor:
    """Simple SWE-Agent executor without complex dependencies."""
    
    def __init__(self):
        """Initialize the simple SWE-Agent executor."""
        self.active_processes: Dict[str, subprocess.Popen] = {}
        self.process_threads: Dict[str, threading.Thread] = {}
        self.output_callbacks: Dict[str, List[Callable]] = {}
        self.lock = threading.RLock()
        
        # Calculate paths
        current_file = Path(__file__).resolve()
        self.project_root = current_file.parent.parent.parent
        self.swe_venv_python = self.project_root / "swe_venv" / "bin" / "python"
        self.swe_agent_path = self.project_root / "swe-agent"
        
        logger.info(f"SimpleSWEExecutor initialized")
        logger.info(f"Project root: {self.project_root}")
        logger.info(f"SWE venv: {self.swe_venv_python}")
        logger.info(f"SWE agent: {self.swe_agent_path}")
        
        # Verify installation
        if not self._verify_installation():
            raise RuntimeError("SWE-Agent installation not found or invalid")
            
    def _verify_installation(self) -> bool:
        """Verify SWE-Agent installation."""
        if not self.swe_venv_python.exists():
            logger.error(f"SWE-Agent Python environment not found: {self.swe_venv_python}")
            return False
            
        if not (self.swe_agent_path / "sweagent" / "__init__.py").exists():
            logger.error(f"SWE-Agent module not found: {self.swe_agent_path}")
            return False
            
        return True

    def _validate_repository(self, repo_path: str, config: SimpleSWEConfig) -> tuple[bool, str]:
        """Validate repository state and requirements."""
        repo_path_obj = Path(repo_path)

        # Check if path exists
        if not repo_path_obj.exists():
            return False, f"Repository path does not exist: {repo_path}"

        # Check if it's a directory
        if not repo_path_obj.is_dir():
            return False, f"Repository path is not a directory: {repo_path}"

        # Check if Git validation is enabled
        if not config.enable_git_validation:
            return True, "Git validation disabled"

        # Check if it's a Git repository
        git_dir = repo_path_obj / ".git"
        if not git_dir.exists():
            return False, f"Not a Git repository: {repo_path}"

        try:
            # Check for uncommitted changes
            result = subprocess.run(
                ["git", "status", "--porcelain"],
                cwd=repo_path,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                return False, f"Git status check failed: {result.stderr}"

            # If there are uncommitted changes
            if result.stdout.strip():
                if config.auto_commit_changes:
                    # Auto-commit changes
                    try:
                        subprocess.run(
                            ["git", "add", "."],
                            cwd=repo_path,
                            check=True,
                            timeout=60
                        )
                        subprocess.run(
                            ["git", "commit", "-m", "Auto-commit for SWE-Agent analysis"],
                            cwd=repo_path,
                            check=True,
                            timeout=60
                        )
                        logger.info(f"Auto-committed changes in {repo_path}")
                        return True, "Auto-committed uncommitted changes"
                    except subprocess.CalledProcessError as e:
                        return False, f"Failed to auto-commit changes: {e}"
                else:
                    return False, f"Repository has uncommitted changes. Please commit or stash them first."

            return True, "Repository validation passed"

        except subprocess.TimeoutExpired:
            return False, "Git validation timed out"
        except Exception as e:
            return False, f"Git validation error: {e}"

    def create_config_file(self, config: SimpleSWEConfig) -> str:
        """Create a temporary configuration file for SWE-Agent using correct format."""
        config_data = {
            "agent": {
                "templates": {
                    "system_template": "You are a helpful assistant that can interact with a computer to solve tasks.",
                    "instance_template": (
                        "I need you to help me with the following task:\n"
                        "{{problem_statement}}\n\n"
                        "Please use the available tools to complete this task. "
                        "Work step by step and explain your reasoning."
                    ),
                    "next_step_template": "OBSERVATION:\n{{observation}}",
                    "next_step_no_output_template": "Your command ran successfully and did not produce any output."
                },
                "tools": {
                    "env_variables": {
                        "WINDOW": 200,
                        "OVERLAP": 5,
                        "CONTINUOUS_MODE": "true"
                    },
                    "bundles": [
                        {"path": "tools/registry"},
                        {"path": "tools/windowed"},
                        {"path": "tools/search"},
                        {"path": "tools/windowed_edit_linting"},
                        {"path": "tools/submit"}
                    ],
                    "enable_bash_tool": True,
                    "parse_function": {
                        "type": "function_calling"
                    }
                },
                "history_processors": [
                    {
                        "type": "last_n_observations",
                        "n": 10
                    }
                ]
            }
        }
        
        # Create temporary config file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            import yaml
            yaml.dump(config_data, f, default_flow_style=False)
            return f.name
            
    def start_agent(self, session_id: str, config: SimpleSWEConfig) -> tuple[bool, str]:
        """Start SWE-Agent execution for a session."""
        with self.lock:
            if session_id in self.active_processes:
                logger.warning(f"SWE-Agent already running for session {session_id}")
                return False, f"SWE-Agent already running for session {session_id}"

        try:
            # Validate repository first
            is_valid, validation_message = self._validate_repository(config.repo_path, config)
            if not is_valid:
                logger.error(f"Repository validation failed for session {session_id}: {validation_message}")
                return False, f"Repository validation failed: {validation_message}"

            logger.info(f"Repository validation passed for session {session_id}: {validation_message}")

            # Check API keys
            if "ANTHROPIC_API_KEY" not in os.environ and "OPENAI_API_KEY" not in os.environ:
                error_msg = "No API keys found. Please set ANTHROPIC_API_KEY or OPENAI_API_KEY environment variable."
                logger.error(error_msg)
                return False, error_msg
            # Create config file
            config_file = self.create_config_file(config)
            
            # Prepare output directory
            output_dir = f"/tmp/swe_agent_output_{session_id}"
            os.makedirs(output_dir, exist_ok=True)
            
            # Prepare command with correct format
            cmd = [
                str(self.swe_venv_python),
                "-m", "sweagent", "run",
                "--config", config_file,
                "--agent.model.name", config.model_name,
                "--env.repo.type", "local",
                "--env.repo.path", f'"{config.repo_path}"',
                "--problem_statement.text", config.problem_statement,
                "--output_dir", output_dir
            ]
            
            # Prepare environment
            env = os.environ.copy()
            
            # Set API keys
            if "ANTHROPIC_API_KEY" not in env and "OPENAI_API_KEY" not in env:
                logger.warning("No API keys found in environment")
            
            # Start process
            logger.info(f"Starting SWE-Agent for session {session_id}")
            logger.info(f"Command: {' '.join(cmd)}")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env,
                cwd=str(self.swe_agent_path),
                bufsize=1,  # Line buffered
                universal_newlines=True
            )
            
            with self.lock:
                self.active_processes[session_id] = process
                
            # Start output monitoring thread
            monitor_thread = threading.Thread(
                target=self._monitor_process_output,
                args=(session_id, process, config_file),
                daemon=True
            )
            monitor_thread.start()
            
            with self.lock:
                self.process_threads[session_id] = monitor_thread
                
            logger.info(f"SWE-Agent started successfully for session {session_id}")
            return True, "SWE-Agent started successfully"

        except Exception as e:
            logger.exception(f"Error starting SWE-Agent for session {session_id}: {e}")
            return False, f"Error starting SWE-Agent: {str(e)}"
            
    def _monitor_process_output(self, session_id: str, process: subprocess.Popen, config_file: str):
        """Monitor SWE-Agent process output and update session."""
        start_time = time.time()
        last_output_time = start_time
        docker_build_detected = False
        startup_completed = False

        try:
            logger.info(f"Starting output monitoring for session {session_id}")

            # Read output line by line
            while True:
                current_time = time.time()

                # Check if process is still running
                if process.poll() is not None:
                    break

                # Check for startup timeout
                if not startup_completed and (current_time - start_time) > 300:  # 5 minutes
                    logger.error(f"SWE-Agent startup timeout for session {session_id}")
                    self._kill_process_tree(process)
                    break

                # Check for Docker build timeout
                if docker_build_detected and not startup_completed and (current_time - last_output_time) > 600:  # 10 minutes
                    logger.error(f"Docker build timeout for session {session_id}")
                    self._kill_process_tree(process)
                    break

                # Read stdout
                output_received = False
                if process.stdout:
                    line = process.stdout.readline()
                    if line:
                        output_received = True
                        line_stripped = line.strip()
                        self._process_output_line(session_id, line_stripped, "stdout")

                        # Detect Docker build phase
                        if "Building image" in line_stripped or "docker build" in line_stripped.lower():
                            docker_build_detected = True
                            logger.info(f"Docker build detected for session {session_id}")

                        # Detect startup completion
                        if "Starting environment" in line_stripped or "Environment started" in line_stripped:
                            startup_completed = True
                            logger.info(f"SWE-Agent startup completed for session {session_id}")

                # Read stderr
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        output_received = True
                        line_stripped = line.strip()
                        self._process_output_line(session_id, line_stripped, "stderr")

                        # Check for critical errors
                        if any(error in line_stripped.lower() for error in [
                            "traceback", "error:", "exception:", "failed", "timeout"
                        ]):
                            logger.warning(f"Potential error detected in session {session_id}: {line_stripped}")

                # Update last output time if we received output
                if output_received:
                    last_output_time = current_time

                time.sleep(0.1)  # Small delay to prevent CPU spinning
                
            # Process finished, get final output
            stdout, stderr = process.communicate()
            
            if stdout:
                for line in stdout.split('\n'):
                    if line.strip():
                        self._process_output_line(session_id, line.strip(), "stdout")
                        
            if stderr:
                for line in stderr.split('\n'):
                    if line.strip():
                        self._process_output_line(session_id, line.strip(), "stderr")
                        
            # Log final status
            return_code = process.returncode
            if return_code == 0:
                logger.info(f"SWE-Agent completed successfully for session {session_id}")
            else:
                logger.error(f"SWE-Agent failed for session {session_id} with return code {return_code}")

        except Exception as e:
            logger.exception(f"Error monitoring output for session {session_id}: {e}")
        finally:
            # Cleanup
            try:
                os.unlink(config_file)
            except:
                pass

            with self.lock:
                self.active_processes.pop(session_id, None)
                self.process_threads.pop(session_id, None)

    def _kill_process_tree(self, process: subprocess.Popen):
        """Kill a process and all its children."""
        try:
            if process.poll() is None:  # Process is still running
                # Try to get process tree
                try:
                    parent = psutil.Process(process.pid)
                    children = parent.children(recursive=True)

                    # Kill children first
                    for child in children:
                        try:
                            child.terminate()
                        except psutil.NoSuchProcess:
                            pass

                    # Wait for children to terminate
                    psutil.wait_procs(children, timeout=5)

                    # Kill any remaining children
                    for child in children:
                        try:
                            if child.is_running():
                                child.kill()
                        except psutil.NoSuchProcess:
                            pass

                    # Kill parent
                    parent.terminate()
                    try:
                        parent.wait(timeout=5)
                    except psutil.TimeoutExpired:
                        parent.kill()

                except psutil.NoSuchProcess:
                    # Process already terminated
                    pass
                except Exception as e:
                    logger.warning(f"Error killing process tree: {e}")
                    # Fallback to simple kill
                    try:
                        process.terminate()
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()

        except Exception as e:
            logger.error(f"Failed to kill process tree: {e}")
                
    def _process_output_line(self, session_id: str, line: str, stream: str):
        """Process a single output line from SWE-Agent."""
        try:
            # Log the output
            if stream == "stderr":
                logger.warning(f"SWE-Agent stderr [{session_id}]: {line}")
            else:
                logger.info(f"SWE-Agent stdout [{session_id}]: {line}")
                
            # Notify callbacks
            callbacks = self.output_callbacks.get(session_id, [])
            for callback in callbacks:
                try:
                    callback(session_id, line, stream)
                except Exception as e:
                    logger.error(f"Error in output callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error processing output line for session {session_id}: {e}")
            
    def stop_agent(self, session_id: str) -> bool:
        """Stop SWE-Agent execution for a session."""
        with self.lock:
            process = self.active_processes.get(session_id)
            
        if not process:
            logger.warning(f"No active SWE-Agent process for session {session_id}")
            return False
            
        try:
            logger.info(f"Stopping SWE-Agent for session {session_id}")
            
            # Terminate process gracefully
            process.terminate()
            
            # Wait for process to finish
            try:
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                logger.warning(f"SWE-Agent process for session {session_id} did not terminate gracefully, killing...")
                process.kill()
                process.wait()
                
            logger.info(f"SWE-Agent stopped for session {session_id}")
            return True
            
        except Exception as e:
            logger.exception(f"Error stopping SWE-Agent for session {session_id}: {e}")
            return False
            
    def is_running(self, session_id: str) -> bool:
        """Check if SWE-Agent is running for a session."""
        with self.lock:
            process = self.active_processes.get(session_id)
            
        if not process:
            return False
            
        return process.poll() is None
        
    def add_output_callback(self, session_id: str, callback: Callable[[str, str, str], None]):
        """Add a callback for SWE-Agent output."""
        with self.lock:
            if session_id not in self.output_callbacks:
                self.output_callbacks[session_id] = []
            self.output_callbacks[session_id].append(callback)
            
    def cleanup_session(self, session_id: str):
        """Clean up resources for a session."""
        # Stop the agent if running
        if self.is_running(session_id):
            self.stop_agent(session_id)
            
        # Remove callbacks
        with self.lock:
            self.output_callbacks.pop(session_id, None)
            
        logger.info(f"Cleaned up session {session_id}")


# Global simple executor instance
simple_swe_executor = None


def get_simple_swe_executor() -> SimpleSWEExecutor:
    """Get or create the global simple SWE-Agent executor instance."""
    global simple_swe_executor
    
    if simple_swe_executor is None:
        simple_swe_executor = SimpleSWEExecutor()
        
    return simple_swe_executor
