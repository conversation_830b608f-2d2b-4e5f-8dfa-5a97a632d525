"""
Workspace Manager for VS Code Integration
Handles automatic workspace detection, project analysis, and context management.
"""

import os
import json
import logging
import subprocess
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class ProjectInfo:
    """Information about a project workspace."""
    path: str
    name: str
    languages: List[str]
    main_files: List[str]
    dependencies: Dict[str, Any]
    git_info: Dict[str, str]
    file_count: int
    directory_count: int
    size_mb: float
    last_modified: str
    analysis_timestamp: str


@dataclass
class WorkspaceContext:
    """Context information for a workspace."""
    workspace_path: str
    project_info: Optional[ProjectInfo]
    is_valid: bool
    error_message: Optional[str]
    auto_analysis_enabled: bool = True


class WorkspaceManager:
    """Manages workspace detection and project analysis for VS Code integration."""
    
    def __init__(self):
        """Initialize the workspace manager."""
        self.active_workspaces: Dict[str, WorkspaceContext] = {}
        self.project_cache: Dict[str, ProjectInfo] = {}
        self.lock = threading.RLock()
        
    def register_workspace(self, workspace_path: str, client_id: str = "default") -> WorkspaceContext:
        """Register a new workspace from VS Code."""
        try:
            workspace_path = os.path.abspath(workspace_path)
            logger.info(f"Registering workspace: {workspace_path} for client {client_id}")
            
            # Validate workspace
            is_valid, error_message = self._validate_workspace(workspace_path)
            
            # Create workspace context
            context = WorkspaceContext(
                workspace_path=workspace_path,
                project_info=None,
                is_valid=is_valid,
                error_message=error_message
            )
            
            if is_valid:
                # Analyze project if valid
                project_info = self._analyze_project(workspace_path)
                context.project_info = project_info
                
                # Cache the analysis
                with self.lock:
                    self.project_cache[workspace_path] = project_info
            
            # Store workspace context
            with self.lock:
                self.active_workspaces[client_id] = context
                
            logger.info(f"Workspace registered successfully: {workspace_path}")
            return context
            
        except Exception as e:
            logger.exception(f"Error registering workspace {workspace_path}: {e}")
            error_context = WorkspaceContext(
                workspace_path=workspace_path,
                project_info=None,
                is_valid=False,
                error_message=f"Registration failed: {str(e)}"
            )
            with self.lock:
                self.active_workspaces[client_id] = error_context
            return error_context
    
    def get_workspace_context(self, client_id: str = "default") -> Optional[WorkspaceContext]:
        """Get workspace context for a client."""
        with self.lock:
            return self.active_workspaces.get(client_id)
    
    def get_default_repo_path(self, client_id: str = "default") -> Optional[str]:
        """Get the default repository path for SWE-Agent sessions."""
        context = self.get_workspace_context(client_id)
        if context and context.is_valid:
            return context.workspace_path
        return None
    
    def _validate_workspace(self, workspace_path: str) -> Tuple[bool, Optional[str]]:
        """Validate a workspace path."""
        try:
            path_obj = Path(workspace_path)
            
            # Check if path exists
            if not path_obj.exists():
                return False, f"Workspace path does not exist: {workspace_path}"
            
            # Check if it's a directory
            if not path_obj.is_dir():
                return False, f"Workspace path is not a directory: {workspace_path}"
            
            # Check if it's readable
            if not os.access(workspace_path, os.R_OK):
                return False, f"Workspace path is not readable: {workspace_path}"
            
            return True, None
            
        except Exception as e:
            return False, f"Workspace validation error: {str(e)}"
    
    def _analyze_project(self, workspace_path: str) -> ProjectInfo:
        """Analyze a project workspace and extract information."""
        try:
            path_obj = Path(workspace_path)
            
            # Basic info
            project_name = path_obj.name
            
            # Detect languages
            languages = self._detect_languages(workspace_path)
            
            # Find main files
            main_files = self._find_main_files(workspace_path, languages)
            
            # Analyze dependencies
            dependencies = self._analyze_dependencies(workspace_path)
            
            # Get Git information
            git_info = self._get_git_info(workspace_path)
            
            # Calculate statistics
            file_count, dir_count, size_mb = self._calculate_stats(workspace_path)
            
            # Get last modified time
            last_modified = self._get_last_modified(workspace_path)
            
            return ProjectInfo(
                path=workspace_path,
                name=project_name,
                languages=languages,
                main_files=main_files,
                dependencies=dependencies,
                git_info=git_info,
                file_count=file_count,
                directory_count=dir_count,
                size_mb=size_mb,
                last_modified=last_modified,
                analysis_timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            logger.exception(f"Error analyzing project {workspace_path}: {e}")
            # Return minimal info on error
            return ProjectInfo(
                path=workspace_path,
                name=Path(workspace_path).name,
                languages=[],
                main_files=[],
                dependencies={},
                git_info={},
                file_count=0,
                directory_count=0,
                size_mb=0.0,
                last_modified="",
                analysis_timestamp=datetime.now().isoformat()
            )
    
    def _detect_languages(self, workspace_path: str) -> List[str]:
        """Detect programming languages in the workspace."""
        language_extensions = {
            'python': ['.py', '.pyx', '.pyi'],
            'javascript': ['.js', '.jsx', '.mjs'],
            'typescript': ['.ts', '.tsx'],
            'java': ['.java'],
            'cpp': ['.cpp', '.cxx', '.cc', '.c++'],
            'c': ['.c', '.h'],
            'csharp': ['.cs'],
            'go': ['.go'],
            'rust': ['.rs'],
            'php': ['.php'],
            'ruby': ['.rb'],
            'swift': ['.swift'],
            'kotlin': ['.kt', '.kts'],
            'scala': ['.scala'],
            'html': ['.html', '.htm'],
            'css': ['.css', '.scss', '.sass'],
            'sql': ['.sql'],
            'shell': ['.sh', '.bash', '.zsh'],
            'yaml': ['.yaml', '.yml'],
            'json': ['.json'],
            'xml': ['.xml'],
            'markdown': ['.md', '.markdown']
        }

        detected_languages = set()

        try:
            # Quick scan of immediate directory only to avoid hanging
            path_obj = Path(workspace_path)
            if path_obj.exists():
                for item in path_obj.iterdir():
                    if item.is_file() and not item.name.startswith('.'):
                        extension = item.suffix.lower()
                        for language, extensions in language_extensions.items():
                            if extension in extensions:
                                detected_languages.add(language)

        except Exception as e:
            logger.warning(f"Error detecting languages in {workspace_path}: {e}")

        return sorted(list(detected_languages))
    
    def _find_main_files(self, workspace_path: str, languages: List[str]) -> List[str]:
        """Find main/entry point files in the workspace."""
        main_files = []
        
        # Common main file patterns
        main_patterns = [
            'main.py', 'app.py', '__main__.py', 'run.py', 'server.py',
            'index.js', 'app.js', 'server.js', 'main.js',
            'index.ts', 'app.ts', 'server.ts', 'main.ts',
            'Main.java', 'Application.java',
            'main.cpp', 'main.c',
            'main.go',
            'main.rs',
            'index.html',
            'README.md', 'readme.md',
            'package.json', 'requirements.txt', 'Cargo.toml', 'pom.xml'
        ]
        
        try:
            path_obj = Path(workspace_path)
            
            for pattern in main_patterns:
                matches = list(path_obj.glob(pattern))
                matches.extend(list(path_obj.glob(f"*/{pattern}")))
                
                for match in matches:
                    if match.is_file():
                        relative_path = str(match.relative_to(path_obj))
                        if relative_path not in main_files:
                            main_files.append(relative_path)
                            
        except Exception as e:
            logger.warning(f"Error finding main files in {workspace_path}: {e}")
            
        return main_files[:10]  # Limit to top 10
    
    def _analyze_dependencies(self, workspace_path: str) -> Dict[str, Any]:
        """Analyze project dependencies."""
        dependencies = {}
        
        try:
            path_obj = Path(workspace_path)
            
            # Python dependencies
            requirements_files = ['requirements.txt', 'pyproject.toml', 'setup.py', 'Pipfile']
            for req_file in requirements_files:
                req_path = path_obj / req_file
                if req_path.exists():
                    dependencies['python'] = req_file
                    break
            
            # Node.js dependencies
            package_json = path_obj / 'package.json'
            if package_json.exists():
                try:
                    with open(package_json, 'r') as f:
                        package_data = json.load(f)
                        dependencies['nodejs'] = {
                            'package_manager': 'npm',
                            'dependencies_count': len(package_data.get('dependencies', {})),
                            'dev_dependencies_count': len(package_data.get('devDependencies', {}))
                        }
                except:
                    dependencies['nodejs'] = 'package.json'
            
            # Other dependency files
            dep_files = {
                'java': ['pom.xml', 'build.gradle'],
                'rust': ['Cargo.toml'],
                'go': ['go.mod'],
                'php': ['composer.json'],
                'ruby': ['Gemfile'],
                'csharp': ['*.csproj', '*.sln']
            }
            
            for lang, files in dep_files.items():
                for dep_file in files:
                    if list(path_obj.glob(dep_file)):
                        dependencies[lang] = dep_file
                        break
                        
        except Exception as e:
            logger.warning(f"Error analyzing dependencies in {workspace_path}: {e}")
            
        return dependencies

    def _get_git_info(self, workspace_path: str) -> Dict[str, str]:
        """Get Git repository information."""
        git_info = {}

        try:
            # Check if it's a Git repository
            git_dir = Path(workspace_path) / '.git'
            if not git_dir.exists():
                return git_info

            # Just mark as Git repository without subprocess calls for now
            git_info['is_git_repo'] = True

            # Try to read branch from .git/HEAD file
            try:
                head_file = git_dir / 'HEAD'
                if head_file.exists():
                    with open(head_file, 'r') as f:
                        head_content = f.read().strip()
                        if head_content.startswith('ref: refs/heads/'):
                            git_info['branch'] = head_content.replace('ref: refs/heads/', '')
            except:
                pass

        except Exception as e:
            logger.warning(f"Error getting Git info for {workspace_path}: {e}")

        return git_info

    def _calculate_stats(self, workspace_path: str) -> Tuple[int, int, float]:
        """Calculate workspace statistics."""
        file_count = 0
        dir_count = 0
        total_size = 0

        try:
            # Quick stats without deep traversal to avoid hanging
            path_obj = Path(workspace_path)
            if path_obj.exists():
                # Count immediate files and directories
                for item in path_obj.iterdir():
                    if item.is_file() and not item.name.startswith('.'):
                        file_count += 1
                        try:
                            total_size += item.stat().st_size
                        except:
                            pass
                    elif item.is_dir() and not item.name.startswith('.'):
                        dir_count += 1

        except Exception as e:
            logger.warning(f"Error calculating stats for {workspace_path}: {e}")

        size_mb = total_size / (1024 * 1024)  # Convert to MB
        return file_count, dir_count, size_mb

    def _get_last_modified(self, workspace_path: str) -> str:
        """Get the last modified time of the workspace."""
        try:
            stat = os.stat(workspace_path)
            return datetime.fromtimestamp(stat.st_mtime).isoformat()
        except Exception as e:
            logger.warning(f"Error getting last modified time for {workspace_path}: {e}")
            return ""

    def generate_project_summary(self, client_id: str = "default") -> str:
        """Generate a human-readable project summary."""
        context = self.get_workspace_context(client_id)

        if not context or not context.is_valid or not context.project_info:
            return "No valid workspace detected. Please open a project folder in VS Code."

        info = context.project_info

        summary_parts = [
            f"📁 **Project: {info.name}**",
            f"📍 Path: `{info.path}`",
            ""
        ]

        if info.languages:
            summary_parts.extend([
                f"🔧 **Languages:** {', '.join(info.languages)}",
                ""
            ])

        if info.main_files:
            summary_parts.extend([
                f"📄 **Key Files:**",
                *[f"  • {file}" for file in info.main_files[:5]],
                ""
            ])

        if info.dependencies:
            summary_parts.extend([
                f"📦 **Dependencies:**",
                *[f"  • {lang}: {dep}" for lang, dep in info.dependencies.items()],
                ""
            ])

        if info.git_info:
            git_parts = ["🔀 **Git Info:**"]
            if 'branch' in info.git_info:
                git_parts.append(f"  • Branch: {info.git_info['branch']}")
            if 'has_uncommitted_changes' in info.git_info:
                status = "✅ Clean" if not info.git_info['has_uncommitted_changes'] else "⚠️ Has uncommitted changes"
                git_parts.append(f"  • Status: {status}")
            summary_parts.extend(git_parts + [""])

        summary_parts.extend([
            f"📊 **Statistics:**",
            f"  • Files: {info.file_count:,}",
            f"  • Directories: {info.directory_count:,}",
            f"  • Size: {info.size_mb:.1f} MB",
            ""
        ])

        return "\n".join(summary_parts)


# Global workspace manager instance
workspace_manager = None


def get_workspace_manager() -> WorkspaceManager:
    """Get or create the global workspace manager instance."""
    global workspace_manager
    
    if workspace_manager is None:
        workspace_manager = WorkspaceManager()
        
    return workspace_manager
