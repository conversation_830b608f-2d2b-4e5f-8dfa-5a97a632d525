"""
Performance monitoring utilities for the AI Coding Agent bridge.
Provides request timing, memory usage tracking, and performance metrics.
"""

import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from contextlib import contextmanager
from functools import wraps
from collections import defaultdict, deque

from bridge.core.enhanced_logging import get_performance_logger

logger = get_performance_logger(__name__)


@dataclass
class PerformanceMetric:
    """Performance metric data."""
    timestamp: str
    metric_type: str
    name: str
    value: float
    unit: str
    context: Dict[str, Any]


@dataclass
class RequestMetrics:
    """Request performance metrics."""
    request_id: str
    endpoint: str
    method: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    status_code: Optional[int] = None
    memory_before: Optional[float] = None
    memory_after: Optional[float] = None
    memory_delta: Optional[float] = None
    context: Dict[str, Any] = None


class PerformanceMonitor:
    """Central performance monitoring system."""
    
    def __init__(self):
        self.metrics: deque = deque(maxlen=10000)  # Keep last 10k metrics
        self.active_requests: Dict[str, RequestMetrics] = {}
        self.endpoint_stats: Dict[str, List[float]] = defaultdict(list)
        self.memory_samples: deque = deque(maxlen=1000)  # Keep last 1k samples
        self.cpu_samples: deque = deque(maxlen=1000)
        self._lock = threading.Lock()
        self._start_background_monitoring()
    
    def _start_background_monitoring(self):
        """Start background thread for system monitoring."""
        def monitor_system():
            while True:
                try:
                    # Sample memory usage
                    memory_info = psutil.virtual_memory()
                    memory_metric = PerformanceMetric(
                        timestamp=datetime.now().isoformat(),
                        metric_type="system",
                        name="memory_usage",
                        value=memory_info.percent,
                        unit="percent",
                        context={
                            "available": memory_info.available,
                            "used": memory_info.used,
                            "total": memory_info.total
                        }
                    )
                    self._add_metric(memory_metric)
                    
                    # Sample CPU usage
                    cpu_percent = psutil.cpu_percent(interval=1)
                    cpu_metric = PerformanceMetric(
                        timestamp=datetime.now().isoformat(),
                        metric_type="system",
                        name="cpu_usage",
                        value=cpu_percent,
                        unit="percent",
                        context={}
                    )
                    self._add_metric(cpu_metric)
                    
                    # Sleep for 30 seconds before next sample
                    time.sleep(30)
                    
                except Exception as e:
                    logger.error(f"Error in background monitoring: {e}")
                    time.sleep(60)  # Wait longer on error
        
        monitor_thread = threading.Thread(target=monitor_system, daemon=True)
        monitor_thread.start()
    
    def _add_metric(self, metric: PerformanceMetric):
        """Add a metric to the collection."""
        with self._lock:
            self.metrics.append(metric)
            
            # Log the metric
            logger.info(
                f"Performance metric: {metric.name} = {metric.value} {metric.unit}",
                context=metric.context,
                metric_type=metric.metric_type,
                metric_name=metric.name,
                metric_value=metric.value
            )
    
    def start_request(self, request_id: str, endpoint: str, method: str, 
                     context: Optional[Dict[str, Any]] = None) -> RequestMetrics:
        """Start tracking a request."""
        memory_before = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        request_metrics = RequestMetrics(
            request_id=request_id,
            endpoint=endpoint,
            method=method,
            start_time=time.time(),
            memory_before=memory_before,
            context=context or {}
        )
        
        with self._lock:
            self.active_requests[request_id] = request_metrics
        
        logger.debug(
            f"Started tracking request {request_id} to {method} {endpoint}",
            request_id=request_id,
            endpoint=endpoint,
            method=method,
            memory_before=memory_before
        )
        
        return request_metrics
    
    def end_request(self, request_id: str, status_code: Optional[int] = None,
                   context: Optional[Dict[str, Any]] = None):
        """End tracking a request."""
        end_time = time.time()
        memory_after = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        with self._lock:
            if request_id not in self.active_requests:
                logger.warning(f"Request {request_id} not found in active requests")
                return
            
            request_metrics = self.active_requests.pop(request_id)
        
        # Calculate metrics
        request_metrics.end_time = end_time
        request_metrics.duration = end_time - request_metrics.start_time
        request_metrics.status_code = status_code
        request_metrics.memory_after = memory_after
        request_metrics.memory_delta = memory_after - request_metrics.memory_before
        
        if context:
            request_metrics.context.update(context)
        
        # Store endpoint statistics
        endpoint_key = f"{request_metrics.method} {request_metrics.endpoint}"
        with self._lock:
            self.endpoint_stats[endpoint_key].append(request_metrics.duration)
            # Keep only last 100 requests per endpoint
            if len(self.endpoint_stats[endpoint_key]) > 100:
                self.endpoint_stats[endpoint_key] = self.endpoint_stats[endpoint_key][-100:]
        
        # Create performance metric
        duration_metric = PerformanceMetric(
            timestamp=datetime.now().isoformat(),
            metric_type="request",
            name="request_duration",
            value=request_metrics.duration,
            unit="seconds",
            context={
                "request_id": request_id,
                "endpoint": request_metrics.endpoint,
                "method": request_metrics.method,
                "status_code": status_code,
                "memory_delta": request_metrics.memory_delta
            }
        )
        self._add_metric(duration_metric)
        
        # Log request completion
        logger.info(
            f"Request {request_id} completed in {request_metrics.duration:.3f}s",
            request_id=request_id,
            endpoint=request_metrics.endpoint,
            method=request_metrics.method,
            duration=request_metrics.duration,
            status_code=status_code,
            memory_delta=request_metrics.memory_delta
        )
    
    def get_endpoint_stats(self, endpoint: str, method: str) -> Dict[str, float]:
        """Get statistics for a specific endpoint."""
        endpoint_key = f"{method} {endpoint}"
        
        with self._lock:
            durations = self.endpoint_stats.get(endpoint_key, [])
        
        if not durations:
            return {}
        
        return {
            "count": len(durations),
            "avg_duration": sum(durations) / len(durations),
            "min_duration": min(durations),
            "max_duration": max(durations),
            "p95_duration": sorted(durations)[int(len(durations) * 0.95)] if len(durations) > 20 else max(durations)
        }
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get current system statistics."""
        memory_info = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent()
        
        return {
            "memory": {
                "percent": memory_info.percent,
                "available_mb": memory_info.available / 1024 / 1024,
                "used_mb": memory_info.used / 1024 / 1024,
                "total_mb": memory_info.total / 1024 / 1024
            },
            "cpu": {
                "percent": cpu_percent
            },
            "active_requests": len(self.active_requests),
            "total_metrics": len(self.metrics)
        }
    
    def get_recent_metrics(self, minutes: int = 5) -> List[PerformanceMetric]:
        """Get metrics from the last N minutes."""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        recent_metrics = []
        with self._lock:
            for metric in reversed(self.metrics):
                metric_time = datetime.fromisoformat(metric.timestamp)
                if metric_time >= cutoff_time:
                    recent_metrics.append(metric)
                else:
                    break
        
        return list(reversed(recent_metrics))


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


@contextmanager
def track_request(request_id: str, endpoint: str, method: str, 
                 context: Optional[Dict[str, Any]] = None):
    """Context manager for tracking request performance."""
    performance_monitor.start_request(request_id, endpoint, method, context)
    try:
        yield
        performance_monitor.end_request(request_id, 200)
    except Exception as e:
        performance_monitor.end_request(request_id, 500, {"error": str(e)})
        raise


def track_performance(endpoint: str, method: str = "FUNCTION"):
    """Decorator for tracking function performance."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            request_id = f"{func.__name__}_{int(time.time() * 1000)}"
            with track_request(request_id, endpoint, method):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def log_performance_metric(name: str, value: float, unit: str, 
                          context: Optional[Dict[str, Any]] = None):
    """Log a custom performance metric."""
    metric = PerformanceMetric(
        timestamp=datetime.now().isoformat(),
        metric_type="custom",
        name=name,
        value=value,
        unit=unit,
        context=context or {}
    )
    performance_monitor._add_metric(metric)
