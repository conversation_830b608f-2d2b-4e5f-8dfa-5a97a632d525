"""
Simple Workspace Manager for VS Code Integration
Minimal implementation to avoid hanging issues.
"""

import os
import logging
from pathlib import Path
from typing import Dict, Optional
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class SimpleProjectInfo:
    """Simple project information."""
    path: str
    name: str
    languages: list
    file_count: int
    analysis_timestamp: str


@dataclass
class SimpleWorkspaceContext:
    """Simple workspace context."""
    workspace_path: str
    project_info: Optional[SimpleProjectInfo]
    is_valid: bool
    error_message: Optional[str]


class SimpleWorkspaceManager:
    """Simple workspace manager to avoid hanging issues."""
    
    def __init__(self):
        """Initialize the simple workspace manager."""
        self.active_workspaces: Dict[str, SimpleWorkspaceContext] = {}
        
    def register_workspace(self, workspace_path: str, client_id: str = "default") -> SimpleWorkspaceContext:
        """Register a new workspace from VS Code."""
        try:
            workspace_path = os.path.abspath(workspace_path)
            logger.info(f"Registering workspace: {workspace_path} for client {client_id}")
            
            # Simple validation
            if not os.path.exists(workspace_path):
                context = SimpleWorkspaceContext(
                    workspace_path=workspace_path,
                    project_info=None,
                    is_valid=False,
                    error_message=f"Workspace path does not exist: {workspace_path}"
                )
                self.active_workspaces[client_id] = context
                return context
            
            if not os.path.isdir(workspace_path):
                context = SimpleWorkspaceContext(
                    workspace_path=workspace_path,
                    project_info=None,
                    is_valid=False,
                    error_message=f"Workspace path is not a directory: {workspace_path}"
                )
                self.active_workspaces[client_id] = context
                return context
            
            # Simple project analysis
            project_info = self._simple_analyze_project(workspace_path)
            
            context = SimpleWorkspaceContext(
                workspace_path=workspace_path,
                project_info=project_info,
                is_valid=True,
                error_message=None
            )
            
            self.active_workspaces[client_id] = context
            logger.info(f"Workspace registered successfully: {workspace_path}")
            return context
            
        except Exception as e:
            logger.exception(f"Error registering workspace {workspace_path}: {e}")
            error_context = SimpleWorkspaceContext(
                workspace_path=workspace_path,
                project_info=None,
                is_valid=False,
                error_message=f"Registration failed: {str(e)}"
            )
            self.active_workspaces[client_id] = error_context
            return error_context
    
    def get_workspace_context(self, client_id: str = "default") -> Optional[SimpleWorkspaceContext]:
        """Get workspace context for a client."""
        return self.active_workspaces.get(client_id)
    
    def get_default_repo_path(self, client_id: str = "default") -> Optional[str]:
        """Get the default repository path for SWE-Agent sessions."""
        context = self.get_workspace_context(client_id)
        if context and context.is_valid:
            return context.workspace_path
        return None
    
    def _simple_analyze_project(self, workspace_path: str) -> SimpleProjectInfo:
        """Simple project analysis without deep traversal."""
        try:
            path_obj = Path(workspace_path)
            project_name = path_obj.name
            
            # Simple language detection
            languages = []
            file_count = 0
            
            # Check for common language indicators
            language_indicators = {
                'python': ['*.py', 'requirements.txt', 'setup.py', 'pyproject.toml'],
                'javascript': ['*.js', 'package.json', '*.jsx'],
                'typescript': ['*.ts', '*.tsx', 'tsconfig.json'],
                'java': ['*.java', 'pom.xml', 'build.gradle'],
                'go': ['*.go', 'go.mod'],
                'rust': ['*.rs', 'Cargo.toml'],
                'html': ['*.html', '*.htm'],
                'css': ['*.css', '*.scss'],
                'markdown': ['*.md', 'README.md']
            }
            
            for language, patterns in language_indicators.items():
                for pattern in patterns:
                    if list(path_obj.glob(pattern)):
                        languages.append(language)
                        break
            
            # Count immediate files
            try:
                for item in path_obj.iterdir():
                    if item.is_file() and not item.name.startswith('.'):
                        file_count += 1
            except:
                pass
            
            return SimpleProjectInfo(
                path=workspace_path,
                name=project_name,
                languages=languages,
                file_count=file_count,
                analysis_timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            logger.exception(f"Error analyzing project {workspace_path}: {e}")
            return SimpleProjectInfo(
                path=workspace_path,
                name=Path(workspace_path).name,
                languages=[],
                file_count=0,
                analysis_timestamp=datetime.now().isoformat()
            )
    
    def generate_project_summary(self, client_id: str = "default") -> str:
        """Generate a human-readable project summary."""
        context = self.get_workspace_context(client_id)
        
        if not context or not context.is_valid or not context.project_info:
            return "No valid workspace detected. Please open a project folder in VS Code."
        
        info = context.project_info
        
        summary_parts = [
            f"📁 **Project: {info.name}**",
            f"📍 Path: `{info.path}`",
            ""
        ]
        
        if info.languages:
            summary_parts.extend([
                f"🔧 **Languages:** {', '.join(info.languages)}",
                ""
            ])
        
        summary_parts.extend([
            f"📊 **Statistics:**",
            f"  • Files: {info.file_count:,}",
            ""
        ])
        
        return "\n".join(summary_parts)


# Global simple workspace manager instance
simple_workspace_manager = None


def get_simple_workspace_manager() -> SimpleWorkspaceManager:
    """Get or create the global simple workspace manager instance."""
    global simple_workspace_manager
    
    if simple_workspace_manager is None:
        simple_workspace_manager = SimpleWorkspaceManager()
        
    return simple_workspace_manager
