"""
Local SWE-Agent executor that runs without Docker for better monitoring and progress tracking.
"""

import os
import sys
import time
import threading
import subprocess
import tempfile
import logging
import json
import shutil
import uuid
from pathlib import Path
from typing import Dict, List, Any, Callable, Optional
from dataclasses import dataclass
from datetime import datetime

# Set up logging
logger = logging.getLogger(__name__)


@dataclass
class LocalSWEConfig:
    """Configuration for local SWE-Agent execution."""
    model_name: str = "claude-3-5-sonnet-20241022"
    repo_path: str = ""
    problem_statement: str = "Analyze this codebase"
    config_file: str = "bridge/config/local_swe_config.yaml"
    output_dir: str = "/tmp/swe_agent_local"
    max_cost: float = 2.0
    max_calls: int = 50
    timeout: int = 300  # 5 minutes
    bypass_git_validation: bool = True  # Allow dirty repositories
    use_repo_copy: bool = True  # Use clean copy of repository


class LocalSWEExecutor:
    """Enhanced SWE-Agent executor that runs locally for better monitoring."""

    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.swe_venv_python = self.project_root / "swe_venv" / "bin" / "python"
        self.active_processes: Dict[str, subprocess.Popen] = {}
        self.process_threads: Dict[str, threading.Thread] = {}
        self.output_callbacks: Dict[str, List[Callable]] = {}
        self.repo_copies: Dict[str, str] = {}  # Track temporary repo copies
        self.lock = threading.Lock()

        logger.info("LocalSWEExecutor initialized")
        logger.info(f"Project root: {self.project_root}")
        logger.info(f"SWE venv: {self.swe_venv_python}")

    def _is_git_repository(self, path: str) -> bool:
        """Check if the given path is a git repository."""
        try:
            result = subprocess.run(
                ["git", "rev-parse", "--git-dir"],
                cwd=path,
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except Exception:
            return False

    def _is_git_dirty(self, path: str) -> bool:
        """Check if the git repository has uncommitted changes."""
        try:
            # Check for staged changes
            result = subprocess.run(
                ["git", "diff", "--cached", "--quiet"],
                cwd=path,
                capture_output=True,
                timeout=10
            )
            if result.returncode != 0:
                return True

            # Check for unstaged changes
            result = subprocess.run(
                ["git", "diff", "--quiet"],
                cwd=path,
                capture_output=True,
                timeout=10
            )
            if result.returncode != 0:
                return True

            # Check for untracked files
            result = subprocess.run(
                ["git", "ls-files", "--others", "--exclude-standard"],
                cwd=path,
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.stdout.strip():
                return True

            return False
        except Exception as e:
            logger.warning(f"Error checking git status: {e}")
            return True  # Assume dirty if we can't check

    def _create_clean_repo_copy(self, original_path: str, session_id: str) -> str:
        """Create a clean copy of the repository for SWE-Agent execution."""
        try:
            # Create unique temporary directory
            temp_dir = tempfile.mkdtemp(prefix=f"swe_agent_repo_{session_id}_")
            repo_name = os.path.basename(original_path.rstrip('/'))
            clean_repo_path = os.path.join(temp_dir, repo_name)

            logger.info(f"Creating clean repository copy for session {session_id}")
            logger.info(f"Original: {original_path}")
            logger.info(f"Copy: {clean_repo_path}")

            if self._is_git_repository(original_path):
                # Use git archive to create a clean copy without uncommitted changes
                result = subprocess.run([
                    "git", "archive", "--format=tar", "HEAD"
                ], cwd=original_path, capture_output=True, timeout=60)

                if result.returncode == 0:
                    # Extract the archive to the temporary directory
                    os.makedirs(clean_repo_path, exist_ok=True)
                    extract_result = subprocess.run([
                        "tar", "-xf", "-", "-C", clean_repo_path
                    ], input=result.stdout, timeout=60)

                    if extract_result.returncode == 0:
                        # Initialize git repository in the clean copy
                        subprocess.run(["git", "init"], cwd=clean_repo_path, capture_output=True)
                        subprocess.run(["git", "add", "."], cwd=clean_repo_path, capture_output=True)
                        subprocess.run([
                            "git", "commit", "-m", "Clean copy for SWE-Agent analysis"
                        ], cwd=clean_repo_path, capture_output=True)

                        logger.info(f"Successfully created clean git repository copy: {clean_repo_path}")
                        return clean_repo_path
                    else:
                        logger.warning("Failed to extract git archive, falling back to file copy")
                else:
                    logger.warning("Failed to create git archive, falling back to file copy")

            # Fallback: copy files excluding .git and common ignore patterns
            self._copy_repository_files(original_path, clean_repo_path)

            # Initialize as git repository if original was git
            if self._is_git_repository(original_path):
                subprocess.run(["git", "init"], cwd=clean_repo_path, capture_output=True)
                subprocess.run(["git", "add", "."], cwd=clean_repo_path, capture_output=True)
                subprocess.run([
                    "git", "commit", "-m", "Clean copy for SWE-Agent analysis"
                ], cwd=clean_repo_path, capture_output=True)

            logger.info(f"Successfully created repository copy: {clean_repo_path}")
            return clean_repo_path

        except Exception as e:
            logger.error(f"Error creating clean repository copy: {e}")
            # Cleanup on failure
            if 'temp_dir' in locals():
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass
            raise

    def _copy_repository_files(self, src: str, dst: str):
        """Copy repository files excluding version control and temporary files."""
        ignore_patterns = {
            '.git', '.svn', '.hg', '.bzr',
            '__pycache__', '.pytest_cache', '.mypy_cache',
            'node_modules', '.npm', '.yarn',
            '.venv', 'venv', '.env',
            '.DS_Store', 'Thumbs.db',
            '*.pyc', '*.pyo', '*.pyd',
            '.coverage', '.tox', '.nox'
        }

        def should_ignore(name: str) -> bool:
            return any(
                name == pattern or
                (pattern.startswith('*') and name.endswith(pattern[1:])) or
                (pattern.endswith('*') and name.startswith(pattern[:-1]))
                for pattern in ignore_patterns
            )

        os.makedirs(dst, exist_ok=True)

        for root, dirs, files in os.walk(src):
            # Filter out ignored directories
            dirs[:] = [d for d in dirs if not should_ignore(d)]

            # Calculate relative path
            rel_path = os.path.relpath(root, src)
            if rel_path == '.':
                dst_dir = dst
            else:
                dst_dir = os.path.join(dst, rel_path)
                os.makedirs(dst_dir, exist_ok=True)

            # Copy files
            for file in files:
                if not should_ignore(file):
                    src_file = os.path.join(root, file)
                    dst_file = os.path.join(dst_dir, file)
                    try:
                        shutil.copy2(src_file, dst_file)
                    except Exception as e:
                        logger.warning(f"Failed to copy {src_file}: {e}")

    def _cleanup_repo_copy(self, session_id: str):
        """Clean up temporary repository copy."""
        if session_id in self.repo_copies:
            repo_path = self.repo_copies[session_id]
            try:
                # Get the parent temp directory
                temp_dir = os.path.dirname(repo_path)
                shutil.rmtree(temp_dir)
                logger.info(f"Cleaned up temporary repository copy: {repo_path}")
            except Exception as e:
                logger.warning(f"Failed to cleanup repository copy {repo_path}: {e}")
            finally:
                del self.repo_copies[session_id]
        
    def create_config_file(self, config: LocalSWEConfig) -> str:
        """Create a temporary config file for SWE-Agent using standard configuration."""

        # Use standard SWE-Agent configuration without custom fields
        config_data = {
            "agent": {
                "model": {
                    "name": config.model_name,
                    "per_instance_cost_limit": config.max_cost,
                    "per_instance_call_limit": config.max_calls,
                },
                "tools": {
                    "bundles": [
                        {"path": "tools/search"},
                        {"path": "tools/edit_anthropic"},
                        {"path": "tools/submit"}
                    ],
                    "enable_bash_tool": True,
                    "parse_function": {"type": "function_calling"},
                    "execution_timeout": 30,
                },
                "templates": {
                    "system_template": "You are a helpful AI assistant that can analyze codebases.",
                    "instance_template": f"Analyze the codebase in {{{{working_dir}}}}.\n\nProblem: {config.problem_statement}\n\nPlease explore and explain this codebase.",
                }
            },
            "env": {
                "deployment": {
                    "type": "local"
                },
                "repo": {
                    "type": "local",
                    "path": config.repo_path
                }
            },
            "problem_statement": {
                "text": config.problem_statement
            },
            "output_dir": config.output_dir
        }

        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            import yaml
            yaml.dump(config_data, f, default_flow_style=False)
            return f.name
    
    def start_agent(self, session_id: str, config: LocalSWEConfig) -> tuple[bool, str]:
        """Start local SWE-Agent execution with git repository bypass."""
        with self.lock:
            if session_id in self.active_processes:
                logger.warning(f"SWE-Agent already running for session {session_id}")
                return False, f"SWE-Agent already running for session {session_id}"

        try:
            # Debug logging at the very start
            logger.info(f"Starting git bypass logic for session {session_id}")
            logger.info(f"Config: bypass_git_validation={config.bypass_git_validation}, use_repo_copy={config.use_repo_copy}")
            logger.info(f"Repository path: {config.repo_path}")

            # Validate repository
            if not os.path.exists(config.repo_path):
                logger.error(f"Repository path does not exist: {config.repo_path}")
                return False, f"Repository path does not exist: {config.repo_path}"

            # Check API keys
            if "ANTHROPIC_API_KEY" not in os.environ and "OPENAI_API_KEY" not in os.environ:
                logger.error("No API keys found")
                return False, "No API keys found. Please set ANTHROPIC_API_KEY or OPENAI_API_KEY."

            # Handle git repository validation bypass
            actual_repo_path = config.repo_path

            # Debug logging
            logger.info(f"Git bypass configuration: bypass_git_validation={config.bypass_git_validation}, use_repo_copy={config.use_repo_copy}")

            if config.bypass_git_validation and self._is_git_repository(config.repo_path):
                logger.info(f"Repository {config.repo_path} is a git repository, checking dirty status")
                if self._is_git_dirty(config.repo_path):
                    logger.info(f"Repository {config.repo_path} has uncommitted changes")

                    if config.use_repo_copy:
                        # Create clean copy of repository
                        logger.info("Creating clean repository copy to bypass git validation")
                        clean_repo_path = self._create_clean_repo_copy(config.repo_path, session_id)
                        actual_repo_path = clean_repo_path

                        # Track the copy for cleanup
                        with self.lock:
                            self.repo_copies[session_id] = clean_repo_path

                        logger.info(f"Using clean repository copy: {actual_repo_path}")
                    else:
                        logger.info("Bypassing git validation - using original repository")
                else:
                    logger.info("Repository is clean, proceeding with original path")
            elif config.bypass_git_validation:
                logger.info(f"Repository {config.repo_path} is not a git repository, proceeding with original path")
            else:
                logger.info("Git bypass disabled, proceeding with original path")

            # Create config file with actual repository path
            updated_config = LocalSWEConfig(
                model_name=config.model_name,
                repo_path=actual_repo_path,
                problem_statement=config.problem_statement,
                output_dir=config.output_dir,
                max_cost=config.max_cost,
                max_calls=config.max_calls,
                timeout=config.timeout,
                bypass_git_validation=config.bypass_git_validation,
                use_repo_copy=config.use_repo_copy
            )

            config_file = self.create_config_file(updated_config)

            # Prepare output directory
            os.makedirs(config.output_dir, exist_ok=True)
            
            # Prepare command for local execution with git bypass
            cmd = [
                str(self.swe_venv_python),
                "-m", "sweagent", "run",
                "--config", config_file,
                "--env.deployment.type", "local",
                "--env.repo.path", actual_repo_path,
                "--problem_statement.text", updated_config.problem_statement,
                "--output_dir", updated_config.output_dir,
                "--agent.model.name", updated_config.model_name,
                "--agent.model.per_instance_cost_limit", str(updated_config.max_cost),
                "--agent.model.per_instance_call_limit", str(updated_config.max_calls)
            ]

            # Set environment variables for git bypass
            env = os.environ.copy()
            if config.bypass_git_validation:
                # Set environment variables that SWE-Agent might recognize
                env["SWE_AGENT_ALLOW_DIRTY_REPO"] = "true"
                env["SWE_AGENT_SKIP_GIT_VALIDATION"] = "true"
                env["ALLOW_DIRTY_REPO"] = "true"
                env["SKIP_GIT_VALIDATION"] = "true"

                # Note: We don't add CLI flags since they're not recognized
                # The git bypass is handled by using clean repository copies
            
            # Start process
            logger.info(f"Starting local SWE-Agent for session {session_id}")
            logger.info(f"Repository path: {actual_repo_path}")
            logger.info(f"Git bypass enabled: {config.bypass_git_validation}")
            logger.info(f"Using repo copy: {config.use_repo_copy}")
            logger.info(f"Command: {' '.join(cmd)}")

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env,
                cwd=str(self.project_root),
                bufsize=1,
                universal_newlines=True
            )
            
            with self.lock:
                self.active_processes[session_id] = process
            
            # Start output monitoring thread
            monitor_thread = threading.Thread(
                target=self._monitor_local_output,
                args=(session_id, process, config_file),
                daemon=True
            )
            monitor_thread.start()
            
            with self.lock:
                self.process_threads[session_id] = monitor_thread
            
            logger.info(f"Local SWE-Agent started successfully for session {session_id}")
            return True, "Local SWE-Agent started successfully"
            
        except Exception as e:
            logger.exception(f"Error starting local SWE-Agent for session {session_id}: {e}")
            return False, f"Error starting local SWE-Agent: {str(e)}"
    
    def _monitor_local_output(self, session_id: str, process: subprocess.Popen, config_file: str):
        """Monitor local SWE-Agent process output with enhanced progress tracking."""
        start_time = time.time()
        last_output_time = start_time
        
        try:
            logger.info(f"Starting local output monitoring for session {session_id}")
            
            # Send initial progress update
            self._send_progress_update(session_id, "Local SWE-Agent initializing...", "info")
            
            while True:
                current_time = time.time()
                
                # Check if process is still running
                if process.poll() is not None:
                    break
                
                # Check for timeout
                if (current_time - start_time) > 300:  # 5 minutes
                    logger.error(f"Local SWE-Agent timeout for session {session_id}")
                    process.terminate()
                    break
                
                # Read stdout
                output_received = False
                if process.stdout:
                    line = process.stdout.readline()
                    if line:
                        output_received = True
                        line_stripped = line.strip()
                        self._process_local_output_line(session_id, line_stripped, "stdout")
                
                # Read stderr
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        output_received = True
                        line_stripped = line.strip()
                        self._process_local_output_line(session_id, line_stripped, "stderr")
                
                # Update last output time
                if output_received:
                    last_output_time = current_time
                
                time.sleep(0.1)
            
            # Process finished
            stdout, stderr = process.communicate()
            
            if stdout:
                for line in stdout.split('\n'):
                    if line.strip():
                        self._process_local_output_line(session_id, line.strip(), "stdout")
            
            if stderr:
                for line in stderr.split('\n'):
                    if line.strip():
                        self._process_local_output_line(session_id, line.strip(), "stderr")
            
            # Final status
            return_code = process.returncode
            if return_code == 0:
                logger.info(f"Local SWE-Agent completed successfully for session {session_id}")
                self._send_progress_update(session_id, "Analysis completed successfully!", "success")
            else:
                logger.error(f"Local SWE-Agent failed for session {session_id} with return code {return_code}")
                self._send_progress_update(session_id, f"Process failed with code {return_code}", "error")
                
        except Exception as e:
            logger.exception(f"Error monitoring local output for session {session_id}: {e}")
        finally:
            # Cleanup
            try:
                os.unlink(config_file)
            except:
                pass
            
            with self.lock:
                self.active_processes.pop(session_id, None)
                self.process_threads.pop(session_id, None)
    
    def _process_local_output_line(self, session_id: str, line: str, stream: str):
        """Process output line with enhanced progress detection."""
        try:
            # Log the output
            if stream == "stderr":
                logger.warning(f"Local SWE-Agent stderr [{session_id}]: {line}")
            else:
                logger.info(f"Local SWE-Agent stdout [{session_id}]: {line}")
            
            # Enhanced progress detection for local execution
            line_lower = line.lower()
            
            if any(keyword in line_lower for keyword in [
                "starting", "initializing", "loading"
            ]):
                self._send_progress_update(session_id, f"Starting: {line[:60]}...", "info")
            elif any(keyword in line_lower for keyword in [
                "exploring", "reading", "analyzing", "processing"
            ]):
                self._send_progress_update(session_id, f"Analyzing: {line[:60]}...", "progress")
            elif any(keyword in line_lower for keyword in [
                "completed", "finished", "done", "success"
            ]):
                self._send_progress_update(session_id, f"Completed: {line[:60]}...", "success")
            elif any(keyword in line_lower for keyword in [
                "error", "failed", "exception", "traceback"
            ]):
                self._send_progress_update(session_id, f"Error: {line[:60]}...", "error")
            
            # Notify callbacks
            callbacks = self.output_callbacks.get(session_id, [])
            for callback in callbacks:
                try:
                    callback(session_id, line, stream)
                except Exception as e:
                    logger.error(f"Error in output callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error processing local output line for session {session_id}: {e}")
    
    def _send_progress_update(self, session_id: str, message: str, status: str):
        """Send progress update to callbacks."""
        callbacks = self.output_callbacks.get(session_id, [])
        for callback in callbacks:
            try:
                callback(session_id, f"[{status.upper()}] {message}", "progress")
            except Exception as e:
                logger.error(f"Error in progress callback: {e}")
    
    def stop_agent(self, session_id: str) -> bool:
        """Stop local SWE-Agent execution."""
        with self.lock:
            process = self.active_processes.get(session_id)
        
        if not process:
            logger.warning(f"No active local SWE-Agent process for session {session_id}")
            return False
        
        try:
            logger.info(f"Stopping local SWE-Agent for session {session_id}")
            process.terminate()
            
            try:
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                logger.warning(f"Local SWE-Agent process for session {session_id} did not terminate gracefully, killing...")
                process.kill()
                process.wait()
            
            logger.info(f"Local SWE-Agent stopped for session {session_id}")
            return True
            
        except Exception as e:
            logger.exception(f"Error stopping local SWE-Agent for session {session_id}: {e}")
            return False
    
    def is_running(self, session_id: str) -> bool:
        """Check if local SWE-Agent is running."""
        with self.lock:
            process = self.active_processes.get(session_id)
        
        if not process:
            return False
        
        return process.poll() is None
    
    def add_output_callback(self, session_id: str, callback: Callable[[str, str, str], None]):
        """Add a callback for SWE-Agent output."""
        with self.lock:
            if session_id not in self.output_callbacks:
                self.output_callbacks[session_id] = []
            self.output_callbacks[session_id].append(callback)
    
    def cleanup_session(self, session_id: str):
        """Clean up resources for a session including temporary repository copies."""
        if self.is_running(session_id):
            self.stop_agent(session_id)

        # Clean up temporary repository copy if it exists
        self._cleanup_repo_copy(session_id)

        with self.lock:
            self.output_callbacks.pop(session_id, None)

        logger.info(f"Cleaned up local session {session_id}")


# Global local executor instance
local_swe_executor = None


def get_local_swe_executor() -> LocalSWEExecutor:
    """Get or create the global local SWE-Agent executor instance."""
    global local_swe_executor
    
    if local_swe_executor is None:
        local_swe_executor = LocalSWEExecutor()
    
    return local_swe_executor
