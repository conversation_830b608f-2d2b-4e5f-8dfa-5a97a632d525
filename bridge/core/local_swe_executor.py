"""
Local SWE-Agent executor that runs without Docker for better monitoring and progress tracking.
"""

import os
import sys
import time
import threading
import subprocess
import tempfile
import logging
import json
from pathlib import Path
from typing import Dict, List, Any, Callable, Optional
from dataclasses import dataclass
from datetime import datetime

# Set up logging
logger = logging.getLogger(__name__)


@dataclass
class LocalSWEConfig:
    """Configuration for local SWE-Agent execution."""
    model_name: str = "claude-3-5-sonnet-20241022"
    repo_path: str = ""
    problem_statement: str = "Analyze this codebase"
    config_file: str = "bridge/config/local_swe_config.yaml"
    output_dir: str = "/tmp/swe_agent_local"
    max_cost: float = 2.0
    max_calls: int = 50
    timeout: int = 300  # 5 minutes


class LocalSWEExecutor:
    """Enhanced SWE-Agent executor that runs locally for better monitoring."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.swe_venv_python = self.project_root / "swe_venv" / "bin" / "python"
        self.active_processes: Dict[str, subprocess.Popen] = {}
        self.process_threads: Dict[str, threading.Thread] = {}
        self.output_callbacks: Dict[str, List[Callable]] = {}
        self.lock = threading.Lock()
        
        logger.info("LocalSWEExecutor initialized")
        logger.info(f"Project root: {self.project_root}")
        logger.info(f"SWE venv: {self.swe_venv_python}")
        
    def create_config_file(self, config: LocalSWEConfig) -> str:
        """Create a temporary config file for SWE-Agent."""
        # Create a proper working directory for SWE-Agent
        import tempfile
        import os

        # Create a unique working directory
        work_dir = os.path.join(tempfile.gettempdir(), f"swe_agent_work_{os.getpid()}")
        os.makedirs(work_dir, exist_ok=True)

        config_data = {
            "agent": {
                "model": {
                    "name": config.model_name,
                    "per_instance_cost_limit": config.max_cost,
                    "per_instance_call_limit": config.max_calls,
                },
                "tools": {
                    "bundles": [
                        {"path": "tools/search"},
                        {"path": "tools/edit_anthropic"},
                        {"path": "tools/submit"}
                    ],
                    "enable_bash_tool": True,
                    "parse_function": {"type": "function_calling"},
                    "execution_timeout": 30,
                },
                "templates": {
                    "system_template": "You are a helpful AI assistant that can analyze codebases.",
                    "instance_template": f"Analyze the codebase in {{{{working_dir}}}}.\n\nProblem: {config.problem_statement}\n\nPlease explore and explain this codebase.",
                }
            },
            "env": {
                "deployment": {
                    "type": "local",
                    "working_dir": work_dir
                },
                "repo": {
                    "type": "local",
                    "path": config.repo_path
                }
            },
            "problem_statement": {
                "text": config.problem_statement
            },
            "output_dir": config.output_dir
        }

        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            import yaml
            yaml.dump(config_data, f, default_flow_style=False)
            return f.name
    
    def start_agent(self, session_id: str, config: LocalSWEConfig) -> tuple[bool, str]:
        """Start local SWE-Agent execution."""
        with self.lock:
            if session_id in self.active_processes:
                logger.warning(f"SWE-Agent already running for session {session_id}")
                return False, f"SWE-Agent already running for session {session_id}"
        
        try:
            # Validate repository
            if not os.path.exists(config.repo_path):
                return False, f"Repository path does not exist: {config.repo_path}"
            
            # Check API keys
            if "ANTHROPIC_API_KEY" not in os.environ and "OPENAI_API_KEY" not in os.environ:
                return False, "No API keys found. Please set ANTHROPIC_API_KEY or OPENAI_API_KEY."
            
            # Create config file
            config_file = self.create_config_file(config)
            
            # Prepare output directory
            os.makedirs(config.output_dir, exist_ok=True)
            
            # Prepare command for local execution
            cmd = [
                str(self.swe_venv_python),
                "-m", "sweagent", "run",
                "--config", config_file,
                "--env.deployment.type", "local",
                "--env.repo.path", config.repo_path,
                "--problem_statement.text", config.problem_statement,
                "--output_dir", config.output_dir,
                "--agent.model.name", config.model_name,
                "--agent.model.per_instance_cost_limit", str(config.max_cost),
                "--agent.model.per_instance_call_limit", str(config.max_calls)
            ]
            
            # Prepare environment
            env = os.environ.copy()
            
            # Start process
            logger.info(f"Starting local SWE-Agent for session {session_id}")
            logger.info(f"Command: {' '.join(cmd)}")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env,
                cwd=str(self.project_root),
                bufsize=1,
                universal_newlines=True
            )
            
            with self.lock:
                self.active_processes[session_id] = process
            
            # Start output monitoring thread
            monitor_thread = threading.Thread(
                target=self._monitor_local_output,
                args=(session_id, process, config_file),
                daemon=True
            )
            monitor_thread.start()
            
            with self.lock:
                self.process_threads[session_id] = monitor_thread
            
            logger.info(f"Local SWE-Agent started successfully for session {session_id}")
            return True, "Local SWE-Agent started successfully"
            
        except Exception as e:
            logger.exception(f"Error starting local SWE-Agent for session {session_id}: {e}")
            return False, f"Error starting local SWE-Agent: {str(e)}"
    
    def _monitor_local_output(self, session_id: str, process: subprocess.Popen, config_file: str):
        """Monitor local SWE-Agent process output with enhanced progress tracking."""
        start_time = time.time()
        last_output_time = start_time
        
        try:
            logger.info(f"Starting local output monitoring for session {session_id}")
            
            # Send initial progress update
            self._send_progress_update(session_id, "Local SWE-Agent initializing...", "info")
            
            while True:
                current_time = time.time()
                
                # Check if process is still running
                if process.poll() is not None:
                    break
                
                # Check for timeout
                if (current_time - start_time) > 300:  # 5 minutes
                    logger.error(f"Local SWE-Agent timeout for session {session_id}")
                    process.terminate()
                    break
                
                # Read stdout
                output_received = False
                if process.stdout:
                    line = process.stdout.readline()
                    if line:
                        output_received = True
                        line_stripped = line.strip()
                        self._process_local_output_line(session_id, line_stripped, "stdout")
                
                # Read stderr
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        output_received = True
                        line_stripped = line.strip()
                        self._process_local_output_line(session_id, line_stripped, "stderr")
                
                # Update last output time
                if output_received:
                    last_output_time = current_time
                
                time.sleep(0.1)
            
            # Process finished
            stdout, stderr = process.communicate()
            
            if stdout:
                for line in stdout.split('\n'):
                    if line.strip():
                        self._process_local_output_line(session_id, line.strip(), "stdout")
            
            if stderr:
                for line in stderr.split('\n'):
                    if line.strip():
                        self._process_local_output_line(session_id, line.strip(), "stderr")
            
            # Final status
            return_code = process.returncode
            if return_code == 0:
                logger.info(f"Local SWE-Agent completed successfully for session {session_id}")
                self._send_progress_update(session_id, "Analysis completed successfully!", "success")
            else:
                logger.error(f"Local SWE-Agent failed for session {session_id} with return code {return_code}")
                self._send_progress_update(session_id, f"Process failed with code {return_code}", "error")
                
        except Exception as e:
            logger.exception(f"Error monitoring local output for session {session_id}: {e}")
        finally:
            # Cleanup
            try:
                os.unlink(config_file)
            except:
                pass
            
            with self.lock:
                self.active_processes.pop(session_id, None)
                self.process_threads.pop(session_id, None)
    
    def _process_local_output_line(self, session_id: str, line: str, stream: str):
        """Process output line with enhanced progress detection."""
        try:
            # Log the output
            if stream == "stderr":
                logger.warning(f"Local SWE-Agent stderr [{session_id}]: {line}")
            else:
                logger.info(f"Local SWE-Agent stdout [{session_id}]: {line}")
            
            # Enhanced progress detection for local execution
            line_lower = line.lower()
            
            if any(keyword in line_lower for keyword in [
                "starting", "initializing", "loading"
            ]):
                self._send_progress_update(session_id, f"Starting: {line[:60]}...", "info")
            elif any(keyword in line_lower for keyword in [
                "exploring", "reading", "analyzing", "processing"
            ]):
                self._send_progress_update(session_id, f"Analyzing: {line[:60]}...", "progress")
            elif any(keyword in line_lower for keyword in [
                "completed", "finished", "done", "success"
            ]):
                self._send_progress_update(session_id, f"Completed: {line[:60]}...", "success")
            elif any(keyword in line_lower for keyword in [
                "error", "failed", "exception", "traceback"
            ]):
                self._send_progress_update(session_id, f"Error: {line[:60]}...", "error")
            
            # Notify callbacks
            callbacks = self.output_callbacks.get(session_id, [])
            for callback in callbacks:
                try:
                    callback(session_id, line, stream)
                except Exception as e:
                    logger.error(f"Error in output callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error processing local output line for session {session_id}: {e}")
    
    def _send_progress_update(self, session_id: str, message: str, status: str):
        """Send progress update to callbacks."""
        callbacks = self.output_callbacks.get(session_id, [])
        for callback in callbacks:
            try:
                callback(session_id, f"[{status.upper()}] {message}", "progress")
            except Exception as e:
                logger.error(f"Error in progress callback: {e}")
    
    def stop_agent(self, session_id: str) -> bool:
        """Stop local SWE-Agent execution."""
        with self.lock:
            process = self.active_processes.get(session_id)
        
        if not process:
            logger.warning(f"No active local SWE-Agent process for session {session_id}")
            return False
        
        try:
            logger.info(f"Stopping local SWE-Agent for session {session_id}")
            process.terminate()
            
            try:
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                logger.warning(f"Local SWE-Agent process for session {session_id} did not terminate gracefully, killing...")
                process.kill()
                process.wait()
            
            logger.info(f"Local SWE-Agent stopped for session {session_id}")
            return True
            
        except Exception as e:
            logger.exception(f"Error stopping local SWE-Agent for session {session_id}: {e}")
            return False
    
    def is_running(self, session_id: str) -> bool:
        """Check if local SWE-Agent is running."""
        with self.lock:
            process = self.active_processes.get(session_id)
        
        if not process:
            return False
        
        return process.poll() is None
    
    def add_output_callback(self, session_id: str, callback: Callable[[str, str, str], None]):
        """Add a callback for SWE-Agent output."""
        with self.lock:
            if session_id not in self.output_callbacks:
                self.output_callbacks[session_id] = []
            self.output_callbacks[session_id].append(callback)
    
    def cleanup_session(self, session_id: str):
        """Clean up resources for a session."""
        if self.is_running(session_id):
            self.stop_agent(session_id)
        
        with self.lock:
            self.output_callbacks.pop(session_id, None)
        
        logger.info(f"Cleaned up local session {session_id}")


# Global local executor instance
local_swe_executor = None


def get_local_swe_executor() -> LocalSWEExecutor:
    """Get or create the global local SWE-Agent executor instance."""
    global local_swe_executor
    
    if local_swe_executor is None:
        local_swe_executor = LocalSWEExecutor()
    
    return local_swe_executor
