"""
Continuous Execution Manager for long-running AI Coding Agent sessions.
"""

import asyncio
import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum

from bridge.core.session_manager import <PERSON><PERSON><PERSON><PERSON>, SessionStatus, SessionConfig
from bridge.core.enhanced_config import enhanced_config

logger = logging.getLogger(__name__)


class ExecutionMode(Enum):
    """Execution mode enumeration."""
    STANDARD = "standard"
    CONTINUOUS = "continuous"
    PARALLEL = "parallel"


@dataclass
class ContinuousTask:
    """Represents a continuous task."""
    task_id: str
    session_id: str
    description: str
    priority: int = 1
    created_at: datetime = None
    status: str = "pending"
    progress: float = 0.0
    sub_tasks: List['ContinuousTask'] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.sub_tasks is None:
            self.sub_tasks = []


class ContinuousExecutionManager:
    """Manages continuous and parallel execution of AI Coding Agent sessions."""
    
    def __init__(self, session_manager: SessionManager = None, enhanced_config: dict = None):
        """Initialize the continuous execution manager."""
        self.session_manager = session_manager
        self.active_tasks: Dict[str, ContinuousTask] = {}
        self.task_queue: List[ContinuousTask] = []
        self.worker_threads: List[threading.Thread] = []
        self.heartbeat_thread: Optional[threading.Thread] = None
        self.is_running = False
        self.lock = threading.RLock()

        # Configuration with safe defaults
        self.config = {}
        if enhanced_config and isinstance(enhanced_config, dict):
            self.config = enhanced_config.get("continuous", {})

        # Safe configuration access with defaults
        parallel_config = self.config.get("parallel", {}) if self.config else {}
        agent_config = self.config.get("agent", {}) if self.config else {}
        session_config = self.config.get("session", {}) if self.config else {}

        self.max_workers = parallel_config.get("max_workers", 4)
        self.heartbeat_interval = agent_config.get("heartbeat_interval", 300)
        self.auto_save_interval = session_config.get("auto_save_interval", 600)

        # Default timeout configurations
        self.default_execution_timeout = 1800  # 30 minutes
        self.default_total_timeout = 0  # Unlimited for continuous mode
        self.default_max_timeouts = 20
        
        # Callbacks
        self.progress_callbacks: List[Callable] = []
        self.completion_callbacks: List[Callable] = []

    def initialize_session_manager(self, session_manager):
        """Initialize the session manager after creation."""
        self.session_manager = session_manager
        logger.info("Continuous execution manager session manager initialized")

    def start(self):
        """Start the continuous execution manager."""
        if self.is_running:
            logger.warning("Continuous execution manager is already running")
            return

        if not self.session_manager:
            logger.warning("Session manager not set, continuous execution manager will not start")
            return

        self.is_running = True
        logger.info("Starting continuous execution manager")
        
        # Start worker threads
        for i in range(self.max_workers):
            worker = threading.Thread(
                target=self._worker_loop,
                name=f"ContinuousWorker-{i}",
                daemon=True
            )
            worker.start()
            self.worker_threads.append(worker)
            
        # Start heartbeat thread
        self.heartbeat_thread = threading.Thread(
            target=self._heartbeat_loop,
            name="ContinuousHeartbeat",
            daemon=True
        )
        self.heartbeat_thread.start()
        
        logger.info(f"Started {self.max_workers} worker threads and heartbeat monitor")
        
    def stop(self):
        """Stop the continuous execution manager."""
        if not self.is_running:
            return
            
        logger.info("Stopping continuous execution manager")
        self.is_running = False
        
        # Wait for threads to finish
        for worker in self.worker_threads:
            worker.join(timeout=5)
            
        if self.heartbeat_thread:
            self.heartbeat_thread.join(timeout=5)
            
        self.worker_threads.clear()
        self.heartbeat_thread = None
        
        logger.info("Continuous execution manager stopped")
        
    def create_continuous_session(self, config: SessionConfig,
                                task_description: str = None,
                                enable_parallel: bool = False) -> str:
        """
        Create a session configured for continuous execution.

        Args:
            config: Session configuration
            task_description: Description of the task
            enable_parallel: Whether to enable parallel sub-tasks

        Returns:
            Session ID
        """
        # Override configuration for continuous mode with extended timeouts
        continuous_config = SessionConfig(
            model_name=config.model_name,
            repo_path=config.repo_path,
            problem_statement=config.problem_statement,
            tools={
                **(config.tools or {}),
                "execution_timeout": self.default_execution_timeout,  # 30 minutes
                "total_execution_timeout": self.default_total_timeout,  # Unlimited for continuous mode
                "max_consecutive_execution_timeouts": self.default_max_timeouts  # More tolerance for long tasks
            },
            environment={
                **(config.environment or {}),
                "continuous_mode": True,
                "auto_resume": True,
                "extended_timeouts": True
            },
            retry_config={
                **(config.retry_config or {}),
                "max_attempts": 15,  # More attempts for complex tasks
                "backoff_factor": 1.2
            }
        )
        
        session_id = self.session_manager.create_session(continuous_config)
        
        # Create continuous task
        task = ContinuousTask(
            task_id=f"task_{session_id}",
            session_id=session_id,
            description=task_description or config.problem_statement,
            priority=1
        )
        
        with self.lock:
            self.active_tasks[task.task_id] = task
            self.task_queue.append(task)
            
        logger.info(f"Created continuous session {session_id} with task {task.task_id}")
        return session_id
        
    def add_sub_task(self, parent_task_id: str, sub_task_description: str,
                    priority: int = 1) -> str:
        """
        Add a sub-task to an existing continuous task.
        
        Args:
            parent_task_id: Parent task ID
            sub_task_description: Description of the sub-task
            priority: Task priority (higher = more important)
            
        Returns:
            Sub-task ID
        """
        with self.lock:
            parent_task = self.active_tasks.get(parent_task_id)
            if not parent_task:
                raise ValueError(f"Parent task {parent_task_id} not found")
                
            # Create sub-task session
            parent_session = self.session_manager.get_session(parent_task.session_id)
            if not parent_session:
                raise ValueError(f"Parent session {parent_task.session_id} not found")
                
            sub_config = SessionConfig(
                model_name=parent_session.config.model_name,
                repo_path=parent_session.config.repo_path,
                problem_statement=sub_task_description,
                tools=parent_session.config.tools,
                environment=parent_session.config.environment,
                retry_config=parent_session.config.retry_config
            )
            
            sub_session_id = self.session_manager.create_session(sub_config)
            
            sub_task = ContinuousTask(
                task_id=f"subtask_{sub_session_id}",
                session_id=sub_session_id,
                description=sub_task_description,
                priority=priority
            )
            
            parent_task.sub_tasks.append(sub_task)
            self.active_tasks[sub_task.task_id] = sub_task
            
            # Insert into queue based on priority
            self._insert_task_by_priority(sub_task)
            
        logger.info(f"Added sub-task {sub_task.task_id} to parent {parent_task_id}")
        return sub_task.task_id
        
    def _insert_task_by_priority(self, task: ContinuousTask):
        """Insert task into queue based on priority."""
        inserted = False
        for i, queued_task in enumerate(self.task_queue):
            if task.priority > queued_task.priority:
                self.task_queue.insert(i, task)
                inserted = True
                break
        if not inserted:
            self.task_queue.append(task)
            
    def _worker_loop(self):
        """Main worker loop for processing tasks."""
        worker_name = threading.current_thread().name
        logger.info(f"Worker {worker_name} started")
        
        while self.is_running:
            try:
                task = self._get_next_task()
                if task:
                    self._process_task(task)
                else:
                    time.sleep(1)  # No tasks available, wait
            except Exception as e:
                logger.exception(f"Error in worker {worker_name}: {e}")
                time.sleep(5)  # Wait before retrying
                
        logger.info(f"Worker {worker_name} stopped")
        
    def _get_next_task(self) -> Optional[ContinuousTask]:
        """Get the next task from the queue."""
        with self.lock:
            if self.task_queue:
                return self.task_queue.pop(0)
        return None
        
    def _process_task(self, task: ContinuousTask):
        """Process a continuous task using SWE-Agent executor."""
        logger.info(f"Processing task {task.task_id}: {task.description}")

        try:
            # Update task status
            task.status = "running"
            self._notify_progress_callbacks(task)

            # Get session and SWE-Agent executor
            session = self.session_manager.get_session(task.session_id)
            if not session:
                raise Exception(f"Session {task.session_id} not found")

            from bridge.core.swe_agent_executor import get_swe_agent_executor, SWEAgentConfig
            executor = get_swe_agent_executor(self.session_manager)

            # Create SWE-Agent configuration with continuous mode settings
            swe_config = SWEAgentConfig(
                model_name=session.config.model_name,
                repo_path=session.config.repo_path,
                problem_statement=session.config.problem_statement,
                execution_timeout=session.config.tools.get("execution_timeout", 1800) if session.config.tools else 1800,
                total_execution_timeout=0,  # Unlimited for continuous mode
                max_consecutive_execution_timeouts=session.config.tools.get("max_consecutive_execution_timeouts", 20) if session.config.tools else 20,
                environment_variables=session.config.environment if session.config.environment else None
            )

            # Add progress callback
            def progress_callback(session_id: str, line: str, stream: str):
                # Update task progress based on output
                if "step" in line.lower() or "progress" in line.lower():
                    # Estimate progress (this could be improved with better parsing)
                    task.progress = min(task.progress + 0.01, 0.95)
                    self._notify_progress_callbacks(task)

            executor.add_output_callback(task.session_id, progress_callback)

            # Start SWE-Agent execution
            success = executor.start_agent(task.session_id, swe_config)
            if not success:
                raise Exception(f"Failed to start SWE-Agent for session {task.session_id}")

            # Monitor session progress
            self._monitor_session_progress(task)

            # Mark as completed
            task.status = "completed"
            task.progress = 1.0
            self._notify_completion_callbacks(task)

        except Exception as e:
            logger.exception(f"Error processing task {task.task_id}: {e}")
            task.status = "failed"
            self._notify_completion_callbacks(task)
            
    def _monitor_session_progress(self, task: ContinuousTask):
        """Monitor session progress and update task."""
        session_id = task.session_id
        
        while self.is_running:
            session = self.session_manager.get_session(session_id)
            if not session:
                break
                
            if session.status in [SessionStatus.COMPLETED, SessionStatus.FAILED, SessionStatus.TERMINATED]:
                break
                
            # Update progress based on session trajectory
            if hasattr(session, 'progress'):
                task.progress = session.progress
                self._notify_progress_callbacks(task)
                
            time.sleep(10)  # Check every 10 seconds
            
    def _heartbeat_loop(self):
        """Heartbeat loop to keep sessions alive."""
        logger.info("Heartbeat monitor started")
        
        while self.is_running:
            try:
                with self.lock:
                    for task in self.active_tasks.values():
                        if task.status == "running":
                            # Send heartbeat to session
                            self.session_manager.update_session_progress(
                                task.session_id, 
                                task.progress,
                                f"Heartbeat at {datetime.now().isoformat()}"
                            )
                            
                time.sleep(self.heartbeat_interval)
                
            except Exception as e:
                logger.exception(f"Error in heartbeat loop: {e}")
                time.sleep(60)  # Wait before retrying
                
        logger.info("Heartbeat monitor stopped")
        
    def _notify_progress_callbacks(self, task: ContinuousTask):
        """Notify progress callbacks."""
        for callback in self.progress_callbacks:
            try:
                callback(task)
            except Exception as e:
                logger.exception(f"Error in progress callback: {e}")
                
    def _notify_completion_callbacks(self, task: ContinuousTask):
        """Notify completion callbacks."""
        for callback in self.completion_callbacks:
            try:
                callback(task)
            except Exception as e:
                logger.exception(f"Error in completion callback: {e}")
                
    def add_progress_callback(self, callback: Callable):
        """Add a progress callback."""
        self.progress_callbacks.append(callback)
        
    def add_completion_callback(self, callback: Callable):
        """Add a completion callback."""
        self.completion_callbacks.append(callback)
        
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task status."""
        with self.lock:
            task = self.active_tasks.get(task_id)
            if task:
                return {
                    "task_id": task.task_id,
                    "session_id": task.session_id,
                    "description": task.description,
                    "status": task.status,
                    "progress": task.progress,
                    "created_at": task.created_at.isoformat(),
                    "sub_tasks": [self.get_task_status(st.task_id) for st in task.sub_tasks]
                }
        return None
        
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """Get all active tasks."""
        with self.lock:
            return [self.get_task_status(task_id) for task_id in self.active_tasks.keys()]


# Global continuous execution manager instance
continuous_manager = ContinuousExecutionManager(session_manager=None, enhanced_config=None)  # Will be initialized later
