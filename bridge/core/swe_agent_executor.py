"""
SWE-Agent Executor for the AI Coding Agent bridge.
Handles actual SWE-Agent process execution and output capture.
"""

import os
import sys
import json
import time
import threading
import subprocess
import tempfile
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from datetime import datetime

from bridge.core.config import config, SWE_AGENT_PATH, PROJECT_ROOT
from bridge.core.session_manager import SessionManager, SessionStatus

logger = logging.getLogger(__name__)


@dataclass
class SWEAgentConfig:
    """Configuration for SWE-Agent execution."""
    model_name: str
    repo_path: str
    problem_statement: str
    output_dir: Optional[str] = None
    config_file: Optional[str] = None
    max_iterations: int = 0  # 0 = unlimited
    execution_timeout: int = 600  # 10 minutes per command
    total_execution_timeout: int = 0  # 0 = unlimited
    max_consecutive_execution_timeouts: int = 10
    environment_variables: Optional[Dict[str, str]] = None


class SWEAgentExecutor:
    """Executes SWE-Agent processes and captures output."""
    
    def __init__(self, session_manager: SessionManager):
        """Initialize the SWE-Agent executor."""
        self.session_manager = session_manager
        self.active_processes: Dict[str, subprocess.Popen] = {}
        self.process_threads: Dict[str, threading.Thread] = {}
        self.output_callbacks: Dict[str, List[Callable]] = {}
        self.lock = threading.RLock()
        
        # Paths
        self.swe_venv_python = PROJECT_ROOT / "swe_venv" / "bin" / "python"
        self.swe_agent_path = SWE_AGENT_PATH
        
        # Verify SWE-Agent installation
        if not self._verify_installation():
            raise RuntimeError("SWE-Agent installation not found or invalid")
            
    def _verify_installation(self) -> bool:
        """Verify SWE-Agent installation."""
        if not self.swe_venv_python.exists():
            logger.error(f"SWE-Agent Python environment not found: {self.swe_venv_python}")
            return False
            
        if not (self.swe_agent_path / "sweagent" / "__init__.py").exists():
            logger.error(f"SWE-Agent module not found: {self.swe_agent_path}")
            return False
            
        return True
        
    def create_config_file(self, config: SWEAgentConfig) -> str:
        """Create a temporary configuration file for SWE-Agent."""
        # Load base continuous configuration
        base_config_path = PROJECT_ROOT / "bridge" / "config" / "continuous_swe_config.yaml"

        if base_config_path.exists():
            import yaml
            with open(base_config_path, 'r') as f:
                config_data = yaml.safe_load(f)
        else:
            # Fallback configuration if file doesn't exist
            config_data = {
                "agent": {
                    "model": {},
                    "tools": {},
                    "max_iterations": 0
                },
                "environment": {
                    "deployment": {"type": "docker", "image": "python:3.11"},
                    "repo": {}
                },
                "problem_statement": {}
            }

        # Override with session-specific settings
        config_data["agent"]["model"]["model_name"] = config.model_name
        config_data["agent"]["tools"]["execution_timeout"] = config.execution_timeout
        config_data["agent"]["tools"]["total_execution_timeout"] = config.total_execution_timeout
        config_data["agent"]["tools"]["max_consecutive_execution_timeouts"] = config.max_consecutive_execution_timeouts
        config_data["agent"]["max_iterations"] = config.max_iterations

        config_data["environment"]["repo"]["path"] = config.repo_path
        config_data["environment"]["repo"]["base_commit"] = "HEAD"

        config_data["problem_statement"]["text"] = config.problem_statement

        # Add environment variables if provided
        if config.environment_variables:
            if "env_variables" not in config_data["environment"]:
                config_data["environment"]["env_variables"] = {}
            config_data["environment"]["env_variables"].update(config.environment_variables)

        # Create temporary config file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            import yaml
            yaml.dump(config_data, f, default_flow_style=False)
            return f.name
            
    def start_agent(self, session_id: str, config: SWEAgentConfig) -> bool:
        """
        Start SWE-Agent execution for a session.
        
        Args:
            session_id: Session ID
            config: SWE-Agent configuration
            
        Returns:
            True if started successfully, False otherwise
        """
        with self.lock:
            if session_id in self.active_processes:
                logger.warning(f"SWE-Agent already running for session {session_id}")
                return False
                
        try:
            # Create config file
            config_file = self.create_config_file(config)
            
            # Prepare output directory
            output_dir = config.output_dir or f"/tmp/swe_agent_output_{session_id}"
            os.makedirs(output_dir, exist_ok=True)
            
            # Prepare command
            cmd = [
                str(self.swe_venv_python),
                "-m", "sweagent", "run",
                "--config", config_file,
                "--output_dir", output_dir
            ]
            
            # Prepare environment
            env = os.environ.copy()
            
            # Set API keys
            anthropic_key = config.get("api_keys", "anthropic")
            openai_key = config.get("api_keys", "openai")
            
            if anthropic_key:
                env["ANTHROPIC_API_KEY"] = anthropic_key
            if openai_key:
                env["OPENAI_API_KEY"] = openai_key
                
            # Add custom environment variables
            if config.environment_variables:
                env.update(config.environment_variables)
                
            # Start process
            logger.info(f"Starting SWE-Agent for session {session_id}")
            logger.debug(f"Command: {' '.join(cmd)}")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env,
                cwd=str(self.swe_agent_path),
                bufsize=1,  # Line buffered
                universal_newlines=True
            )
            
            with self.lock:
                self.active_processes[session_id] = process
                
            # Start output monitoring thread
            monitor_thread = threading.Thread(
                target=self._monitor_process_output,
                args=(session_id, process, config_file),
                daemon=True
            )
            monitor_thread.start()
            
            with self.lock:
                self.process_threads[session_id] = monitor_thread
                
            # Update session status
            self.session_manager.update_session_status(session_id, SessionStatus.RUNNING)
            self.session_manager.update_session_progress(session_id, 0.1, "SWE-Agent started")
            
            logger.info(f"SWE-Agent started successfully for session {session_id}")
            return True

        except Exception as e:
            logger.exception(f"Error starting SWE-Agent for session {session_id}: {e}")
            self.session_manager.update_session_status(
                session_id,
                SessionStatus.FAILED,
                f"Failed to start SWE-Agent: {str(e)}"
            )
            return False

    def _monitor_process_output(self, session_id: str, process: subprocess.Popen, config_file: str):
        """Monitor SWE-Agent process output and update session."""
        try:
            logger.info(f"Starting output monitoring for session {session_id}")

            # Read output line by line
            while True:
                # Check if process is still running
                if process.poll() is not None:
                    break

                # Read stdout
                if process.stdout:
                    line = process.stdout.readline()
                    if line:
                        self._process_output_line(session_id, line.strip(), "stdout")

                # Read stderr
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        self._process_output_line(session_id, line.strip(), "stderr")

                time.sleep(0.1)  # Small delay to prevent CPU spinning

            # Process finished, get final output
            stdout, stderr = process.communicate()

            if stdout:
                for line in stdout.split('\n'):
                    if line.strip():
                        self._process_output_line(session_id, line.strip(), "stdout")

            if stderr:
                for line in stderr.split('\n'):
                    if line.strip():
                        self._process_output_line(session_id, line.strip(), "stderr")

            # Update final status
            return_code = process.returncode
            if return_code == 0:
                self.session_manager.update_session_status(session_id, SessionStatus.COMPLETED)
                self.session_manager.update_session_progress(session_id, 1.0, "SWE-Agent completed successfully")
            else:
                self.session_manager.update_session_status(
                    session_id,
                    SessionStatus.FAILED,
                    f"SWE-Agent exited with code {return_code}"
                )

            logger.info(f"SWE-Agent finished for session {session_id} with return code {return_code}")

        except Exception as e:
            logger.exception(f"Error monitoring output for session {session_id}: {e}")
            self.session_manager.update_session_status(
                session_id,
                SessionStatus.FAILED,
                f"Output monitoring error: {str(e)}"
            )
        finally:
            # Cleanup
            try:
                os.unlink(config_file)
            except:
                pass

            with self.lock:
                self.active_processes.pop(session_id, None)
                self.process_threads.pop(session_id, None)

    def _process_output_line(self, session_id: str, line: str, stream: str):
        """Process a single output line from SWE-Agent."""
        try:
            # Log the output
            if stream == "stderr":
                logger.warning(f"SWE-Agent stderr [{session_id}]: {line}")
            else:
                logger.info(f"SWE-Agent stdout [{session_id}]: {line}")

            # Try to parse as JSON for structured data
            try:
                data = json.loads(line)
                if isinstance(data, dict):
                    self._handle_structured_output(session_id, data)
                    return
            except json.JSONDecodeError:
                pass

            # Handle specific output patterns
            if "step" in line.lower() or "action" in line.lower():
                # Extract step information
                step_data = {
                    "type": "step",
                    "content": line,
                    "timestamp": datetime.now().isoformat(),
                    "stream": stream
                }
                self.session_manager.add_trajectory_step(session_id, step_data)

            elif "error" in line.lower() or "exception" in line.lower():
                # Handle errors
                step_data = {
                    "type": "error",
                    "content": line,
                    "timestamp": datetime.now().isoformat(),
                    "stream": stream
                }
                self.session_manager.add_trajectory_step(session_id, step_data)

            elif "progress" in line.lower() or "%" in line:
                # Try to extract progress information
                self._extract_progress(session_id, line)

            # Notify callbacks
            callbacks = self.output_callbacks.get(session_id, [])
            for callback in callbacks:
                try:
                    callback(session_id, line, stream)
                except Exception as e:
                    logger.error(f"Error in output callback: {e}")

        except Exception as e:
            logger.error(f"Error processing output line for session {session_id}: {e}")

    def _handle_structured_output(self, session_id: str, data: Dict[str, Any]):
        """Handle structured JSON output from SWE-Agent."""
        try:
            # Add to trajectory
            step_data = {
                "type": "structured",
                "data": data,
                "timestamp": datetime.now().isoformat()
            }
            self.session_manager.add_trajectory_step(session_id, step_data)

            # Update progress if available
            if "progress" in data:
                progress = float(data["progress"])
                current_step = data.get("step", "Processing...")
                self.session_manager.update_session_progress(session_id, progress, current_step)

        except Exception as e:
            logger.error(f"Error handling structured output for session {session_id}: {e}")

    def _extract_progress(self, session_id: str, line: str):
        """Extract progress information from output line."""
        try:
            import re

            # Look for percentage patterns
            percent_match = re.search(r'(\d+(?:\.\d+)?)%', line)
            if percent_match:
                progress = float(percent_match.group(1)) / 100.0
                self.session_manager.update_session_progress(session_id, progress, line)
                return

            # Look for step patterns
            step_match = re.search(r'step\s+(\d+)', line, re.IGNORECASE)
            if step_match:
                step_num = int(step_match.group(1))
                # Estimate progress based on step number (assuming max 50 steps)
                progress = min(step_num / 50.0, 0.9)
                self.session_manager.update_session_progress(session_id, progress, f"Step {step_num}")

        except Exception as e:
            logger.error(f"Error extracting progress for session {session_id}: {e}")

    def stop_agent(self, session_id: str) -> bool:
        """
        Stop SWE-Agent execution for a session.

        Args:
            session_id: Session ID

        Returns:
            True if stopped successfully, False otherwise
        """
        with self.lock:
            process = self.active_processes.get(session_id)

        if not process:
            logger.warning(f"No active SWE-Agent process for session {session_id}")
            return False

        try:
            logger.info(f"Stopping SWE-Agent for session {session_id}")

            # Terminate process gracefully
            process.terminate()

            # Wait for process to finish
            try:
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                logger.warning(f"SWE-Agent process for session {session_id} did not terminate gracefully, killing...")
                process.kill()
                process.wait()

            # Update session status
            self.session_manager.update_session_status(session_id, SessionStatus.TERMINATED)

            logger.info(f"SWE-Agent stopped for session {session_id}")
            return True

        except Exception as e:
            logger.exception(f"Error stopping SWE-Agent for session {session_id}: {e}")
            return False

    def is_running(self, session_id: str) -> bool:
        """Check if SWE-Agent is running for a session."""
        with self.lock:
            process = self.active_processes.get(session_id)

        if not process:
            return False

        return process.poll() is None

    def get_process_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get information about the SWE-Agent process for a session."""
        with self.lock:
            process = self.active_processes.get(session_id)

        if not process:
            return None

        return {
            "pid": process.pid,
            "running": process.poll() is None,
            "return_code": process.returncode
        }

    def add_output_callback(self, session_id: str, callback: Callable[[str, str, str], None]):
        """
        Add a callback for SWE-Agent output.

        Args:
            session_id: Session ID
            callback: Callback function (session_id, line, stream)
        """
        with self.lock:
            if session_id not in self.output_callbacks:
                self.output_callbacks[session_id] = []
            self.output_callbacks[session_id].append(callback)

    def remove_output_callback(self, session_id: str, callback: Callable[[str, str, str], None]):
        """Remove an output callback for a session."""
        with self.lock:
            callbacks = self.output_callbacks.get(session_id, [])
            try:
                callbacks.remove(callback)
            except ValueError:
                pass

    def cleanup_session(self, session_id: str):
        """Clean up resources for a session."""
        # Stop the agent if running
        if self.is_running(session_id):
            self.stop_agent(session_id)

        # Remove callbacks
        with self.lock:
            self.output_callbacks.pop(session_id, None)

        logger.info(f"Cleaned up session {session_id}")

    def get_active_sessions(self) -> List[str]:
        """Get list of active session IDs."""
        with self.lock:
            return list(self.active_processes.keys())

    def cleanup_all(self):
        """Clean up all active sessions."""
        active_sessions = self.get_active_sessions()
        for session_id in active_sessions:
            self.cleanup_session(session_id)

        logger.info("Cleaned up all active sessions")


# Global executor instance
swe_agent_executor = None


def get_swe_agent_executor(session_manager: SessionManager = None) -> SWEAgentExecutor:
    """Get or create the global SWE-Agent executor instance."""
    global swe_agent_executor

    if swe_agent_executor is None:
        if session_manager is None:
            from bridge.core.session_manager import session_manager as default_session_manager
            session_manager = default_session_manager

        swe_agent_executor = SWEAgentExecutor(session_manager)

    return swe_agent_executor
