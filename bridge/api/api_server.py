"""
API server for the AI Coding Agent bridge.
"""

import time
import uuid
import threading

from flask import Flask, request, jsonify, g
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit

from bridge.core.config import config
from bridge.integrations.swe_agent_interface import SWEAgentInterface
from bridge.api.completion_endpoints import completion_bp
from bridge.api.chat_endpoints import chat_bp, setup_chat_websocket
from bridge.api.workspace_endpoints import workspace_bp
from bridge.api.file_endpoints import file_bp
from bridge.api.terminal_endpoints import terminal_bp
from bridge.api.swe_tools_endpoints import swe_tools_bp, setup_swe_tools_websocket
from bridge.api.websocket_completion import initialize_completion_websocket
from bridge.core.enhanced_logging import get_api_logger, get_audit_logger, get_performance_logger
from bridge.core.performance_monitor import performance_monitor, track_request

# Set up enhanced logging
logger = get_api_logger(__name__)
audit_logger = get_audit_logger(__name__)
perf_logger = get_performance_logger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*", logger=True, engineio_logger=True)


# Request tracking middleware
@app.before_request
def before_request():
    """Track request start and add request ID."""
    g.request_id = str(uuid.uuid4())
    g.start_time = time.time()

    # Start performance tracking
    performance_monitor.start_request(
        request_id=g.request_id,
        endpoint=request.endpoint or request.path,
        method=request.method,
        context={
            "remote_addr": request.remote_addr,
            "user_agent": request.headers.get("User-Agent", ""),
            "content_length": request.content_length
        }
    )

    # Log request start
    logger.info(
        f"Request started: {request.method} {request.path}",
        request_id=g.request_id,
        method=request.method,
        path=request.path,
        remote_addr=request.remote_addr
    )


@app.after_request
def after_request(response):
    """Track request completion."""
    if hasattr(g, 'request_id'):
        # End performance tracking
        performance_monitor.end_request(
            request_id=g.request_id,
            status_code=response.status_code,
            context={
                "response_size": response.content_length
            }
        )

        # Log request completion
        duration = time.time() - g.start_time if hasattr(g, 'start_time') else 0
        logger.info(
            f"Request completed: {request.method} {request.path} - {response.status_code} in {duration:.3f}s",
            request_id=g.request_id,
            method=request.method,
            path=request.path,
            status_code=response.status_code,
            duration=duration
        )

        # Audit log for sensitive operations
        if request.method in ['POST', 'PUT', 'DELETE'] and response.status_code < 400:
            audit_logger.info(
                f"API operation: {request.method} {request.path}",
                request_id=g.request_id,
                method=request.method,
                path=request.path,
                status_code=response.status_code,
                remote_addr=request.remote_addr
            )

    return response

# Register blueprints
app.register_blueprint(completion_bp)
app.register_blueprint(chat_bp)
# Temporarily disable OAuth auth endpoints to fix conflicts
# app.register_blueprint(oauth_auth_bp)  # Enhanced OAuth authentication endpoints
app.register_blueprint(workspace_bp)
app.register_blueprint(file_bp)
app.register_blueprint(terminal_bp)
app.register_blueprint(swe_tools_bp)

# Set up WebSocket handlers
# Note: Using the enhanced completion WebSocket handler instead of the basic one
# setup_completion_websocket(socketio)  # Disabled in favor of enhanced handler
setup_chat_websocket(socketio)
setup_swe_tools_websocket(socketio)
initialize_completion_websocket(socketio)  # Enhanced WebSocket completion handler

# Initialize SWE-Agent interface
swe_agent = SWEAgentInterface()


@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    logger.debug("Health check requested")

    # Get system stats for health check
    system_stats = performance_monitor.get_system_stats()

    health_data = {
        "status": "ok",
        "timestamp": time.time(),
        "system": system_stats
    }

    logger.debug("Health check completed", context=health_data)
    return jsonify(health_data)


@app.route('/debug/test-import')
def test_import():
    """Test import of simple SWE executor."""
    try:
        logger.info("Testing simple executor import...")
        from bridge.core.simple_swe_executor import get_simple_swe_executor, SimpleSWEConfig
        logger.info("Import successful, creating executor...")
        executor = get_simple_swe_executor()
        logger.info("Executor created successfully")
        return jsonify({
            "status": "success",
            "message": "Simple SWE executor import and creation successful",
            "executor_info": {
                "project_root": str(executor.project_root),
                "swe_venv_exists": executor.swe_venv_python.exists(),
                "swe_agent_exists": executor.swe_agent_path.exists()
            }
        })
    except Exception as e:
        logger.exception("Failed to import simple executor")
        return jsonify({
            "status": "error",
            "message": f"Failed to import simple executor: {str(e)}",
            "error_type": type(e).__name__
        }), 500


@app.route('/api/run', methods=['POST'])
def run_agent():
    """Run the SWE-Agent on a problem statement."""
    try:
        data = request.json
        if not data:
            logger.warning("No data provided in run_agent request", request_id=getattr(g, 'request_id', None))
            return jsonify({"error": "No data provided"}), 400

        problem_statement = data.get('problem_statement')
        if not problem_statement:
            logger.warning("No problem statement provided", request_id=getattr(g, 'request_id', None))
            return jsonify({"error": "No problem statement provided"}), 400

        repo_path = data.get('repo_path')
        if not repo_path:
            logger.warning("No repository path provided", request_id=getattr(g, 'request_id', None))
            return jsonify({"error": "No repository path provided"}), 400

        model_name = data.get('model_name', 'gpt-4')

        logger.info(
            f"Starting SWE-Agent run",
            request_id=getattr(g, 'request_id', None),
            repo_path=repo_path,
            model_name=model_name,
            problem_length=len(problem_statement)
        )

        # Audit log for agent execution
        audit_logger.info(
            f"SWE-Agent execution started",
            request_id=getattr(g, 'request_id', None),
            repo_path=repo_path,
            model_name=model_name
        )

        # Run the agent
        result = swe_agent.run_agent(
            problem_statement=problem_statement,
            repo_path=repo_path,
            model_name=model_name
        )

        logger.info(
            f"SWE-Agent run completed",
            request_id=getattr(g, 'request_id', None),
            result_status=result
        )

        return jsonify({"status": result})

    except Exception as e:
        logger.exception(
            "Error running agent",
            request_id=getattr(g, 'request_id', None),
            error=str(e)
        )
        return jsonify({"error": str(e)}), 500


@app.route('/api/stop', methods=['POST'])
def stop_agent():
    """Stop the current SWE-Agent run."""
    try:
        result = swe_agent.stop_agent()
        return jsonify({"status": "success" if result else "error"})
    
    except Exception as e:
        logger.exception("Error stopping agent")
        return jsonify({"error": str(e)}), 500


@app.route('/api/config', methods=['GET'])
def get_config():
    """Get the current configuration."""
    logger.debug("Configuration requested", request_id=getattr(g, 'request_id', None))
    return jsonify(config.config)


@app.route('/api/performance', methods=['GET'])
def get_performance_stats():
    """Get performance statistics."""
    try:
        logger.debug("Performance stats requested", request_id=getattr(g, 'request_id', None))

        # Get system stats
        system_stats = performance_monitor.get_system_stats()

        # Get recent metrics
        minutes = int(request.args.get('minutes', 5))
        recent_metrics = performance_monitor.get_recent_metrics(minutes)

        # Get endpoint stats for common endpoints
        endpoint_stats = {}
        common_endpoints = [
            ('/api/run', 'POST'),
            ('/api/sessions', 'POST'),
            ('/api/chat/send', 'POST'),
            ('/health', 'GET')
        ]

        for endpoint, method in common_endpoints:
            stats = performance_monitor.get_endpoint_stats(endpoint, method)
            if stats:
                endpoint_stats[f"{method} {endpoint}"] = stats

        performance_data = {
            "system": system_stats,
            "recent_metrics": [
                {
                    "timestamp": metric.timestamp,
                    "type": metric.metric_type,
                    "name": metric.name,
                    "value": metric.value,
                    "unit": metric.unit
                }
                for metric in recent_metrics
            ],
            "endpoint_stats": endpoint_stats
        }

        logger.debug(
            "Performance stats retrieved",
            request_id=getattr(g, 'request_id', None),
            metrics_count=len(recent_metrics),
            endpoints_count=len(endpoint_stats)
        )

        return jsonify(performance_data)

    except Exception as e:
        logger.exception(
            "Error getting performance stats",
            request_id=getattr(g, 'request_id', None),
            error=str(e)
        )
        return jsonify({"error": str(e)}), 500


# Enhanced Session management endpoints using SessionManager
@app.route('/api/sessions', methods=['POST'])
def create_session():
    """Create a new agent session using enhanced SessionManager."""
    try:
        # Import SessionManager for enhanced session management
        from bridge.core.session_manager import session_manager, SessionConfig

        data = request.json
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Validate required fields
        problem_statement = data.get('problem_statement')
        if not problem_statement:
            return jsonify({"error": "No problem statement provided"}), 400

        repo_path = data.get('repo_path')
        if not repo_path:
            return jsonify({"error": "No repository path provided"}), 400

        # Create session configuration
        config_data = SessionConfig(
            model_name=data.get('model_name', 'claude-sonnet-4-20250514'),
            repo_path=repo_path,
            problem_statement=problem_statement,
            tools=data.get('tools'),
            environment=data.get('environment'),
            retry_config=data.get('retry_config')
        )

        # Create session using SessionManager
        session_id = session_manager.create_session(config_data)

        return jsonify({
            "status": "created",
            "session_id": session_id
        }), 201

    except Exception as e:
        logger.exception("Error creating session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/continuous', methods=['POST'])
def create_continuous_session():
    """Create a new continuous execution session."""
    try:
        from bridge.core.continuous_execution_manager import continuous_manager
        from bridge.core.session_manager import SessionConfig, session_manager
        from bridge.core.simple_workspace_manager import get_simple_workspace_manager

        # Initialize continuous manager with session manager if not already done
        if continuous_manager.session_manager is None:
            continuous_manager.initialize_session_manager(session_manager)

        data = request.json
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Validate required fields
        problem_statement = data.get('problem_statement')
        if not problem_statement:
            return jsonify({"error": "No problem statement provided"}), 400

        # Get repository path - use provided path or default from workspace
        repo_path = data.get('repo_path')
        if not repo_path:
            # Try to get default repository path from workspace manager
            workspace_manager = get_simple_workspace_manager()
            client_id = data.get('client_id', 'vscode-extension')
            repo_path = workspace_manager.get_default_repo_path(client_id)

            if not repo_path:
                return jsonify({"error": "No repository path provided and no workspace registered"}), 400

            logger.info(f"Using default repository path from workspace: {repo_path}")

        # Create session configuration
        config_data = SessionConfig(
            model_name=data.get('model_name', 'claude-sonnet-4-20250514'),
            repo_path=repo_path,
            problem_statement=problem_statement,
            tools=data.get('tools'),
            environment=data.get('environment'),
            retry_config=data.get('retry_config')
        )

        # Create continuous session
        task_description = data.get('task_description', problem_statement)
        enable_parallel = data.get('enable_parallel', False)

        session_id = continuous_manager.create_continuous_session(
            config_data,
            task_description,
            enable_parallel
        )

        return jsonify({
            "status": "created",
            "session_id": session_id,
            "continuous_mode": True,
            "parallel_enabled": enable_parallel
        }), 201

    except Exception as e:
        logger.exception("Error creating continuous session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/continuous/<session_id>/subtask', methods=['POST'])
def add_subtask(session_id):
    """Add a sub-task to a continuous session."""
    try:
        from bridge.core.continuous_execution_manager import continuous_manager

        data = request.json
        if not data:
            return jsonify({"error": "No data provided"}), 400

        sub_task_description = data.get('description')
        if not sub_task_description:
            return jsonify({"error": "No sub-task description provided"}), 400

        priority = data.get('priority', 1)

        # Find the task for this session
        task_id = f"task_{session_id}"
        sub_task_id = continuous_manager.add_sub_task(
            task_id,
            sub_task_description,
            priority
        )

        return jsonify({
            "status": "created",
            "sub_task_id": sub_task_id,
            "parent_session_id": session_id
        }), 201

    except Exception as e:
        logger.exception("Error adding sub-task")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/continuous/tasks', methods=['GET'])
def get_continuous_tasks():
    """Get all continuous tasks."""
    try:
        from bridge.core.continuous_execution_manager import continuous_manager

        tasks = continuous_manager.get_all_tasks()
        return jsonify({
            "tasks": tasks,
            "status": "success"
        })

    except Exception as e:
        logger.exception("Error getting continuous tasks")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions', methods=['GET'])
def list_sessions():
    """List all sessions."""
    try:
        from bridge.core.session_manager import session_manager, SessionStatus

        status_filter = request.args.get('status')
        status_enum = None

        if status_filter:
            try:
                status_enum = SessionStatus(status_filter)
            except ValueError:
                return jsonify({"error": f"Invalid status: {status_filter}"}), 400

        sessions = session_manager.list_sessions(status_enum)
        return jsonify({
            "sessions": [session.to_dict() for session in sessions]
        })

    except Exception as e:
        logger.exception("Error listing sessions")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/start', methods=['POST'])
def start_session(session_id):
    """Start a session using SessionManager and simple SWE-Agent executor."""
    try:
        from bridge.core.session_manager import session_manager, SessionStatus

        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404

        # Update session status to running first
        session_manager.update_session_status(session_id, SessionStatus.RUNNING)
        session_manager.update_session_progress(session_id, 0.2, "Starting SWE-Agent...")

        # Try to import and use the simple executor
        try:
            logger.info(f"Attempting to import simple executor for session {session_id}")
            from bridge.core.simple_swe_executor import get_simple_swe_executor, SimpleSWEConfig
            logger.info("Simple executor import successful")

            # Get simple SWE-Agent executor
            logger.info("Getting simple SWE-Agent executor instance")
            executor = get_simple_swe_executor()
            logger.info("Simple SWE-Agent executor instance created")

            # Create simple SWE-Agent configuration
            tools_config = session.config.tools or {}
            swe_config = SimpleSWEConfig(
                model_name=session.config.model_name,
                repo_path=session.config.repo_path,
                problem_statement=session.config.problem_statement,
                execution_timeout=tools_config.get("execution_timeout", 1800),  # 30 minutes default
                total_execution_timeout=tools_config.get("total_execution_timeout", 0),  # Unlimited
                max_consecutive_execution_timeouts=tools_config.get("max_consecutive_execution_timeouts", 20)  # More tolerance
            )
            logger.info(f"SWE-Agent config created: model={swe_config.model_name}, repo={swe_config.repo_path}")

            # Add WebSocket output callback with enhanced progress tracking
            def websocket_callback(session_id: str, line: str, stream: str):
                from datetime import datetime

                # Log all output for debugging
                logger.info(f"SWE-Agent output [{session_id}] ({stream}): {line}")

                # Emit output to WebSocket
                socketio.emit('session_output', {
                    'session_id': session_id,
                    'output': line,
                    'stream': stream,
                    'timestamp': datetime.now().isoformat()
                }, room=f"output_{session_id}")

                # Enhanced progress tracking based on SWE-Agent output patterns
                line_lower = line.lower()

                # Docker/Environment setup phase (40-60%)
                if any(keyword in line_lower for keyword in [
                    "building image", "docker build", "pulling image", "downloading",
                    "setting up environment", "installing dependencies"
                ]):
                    session_manager.update_session_progress(session_id, 0.5, f"Setting up environment: {line[:60]}...")

                # Environment ready phase (60-70%)
                elif any(keyword in line_lower for keyword in [
                    "environment started", "container ready", "environment ready",
                    "starting environment", "environment initialized"
                ]):
                    session_manager.update_session_progress(session_id, 0.65, f"Environment ready: {line[:60]}...")

                # Agent execution phase (70-90%)
                elif any(keyword in line_lower for keyword in [
                    "step", "action", "executing", "running", "command",
                    "observation", "thought", "reasoning", "plan"
                ]):
                    session_manager.update_session_progress(session_id, 0.8, f"Agent working: {line[:60]}...")

                # Completion phase (90-100%)
                elif any(keyword in line_lower for keyword in [
                    "completed", "finished", "done", "success", "final",
                    "summary", "result", "conclusion"
                ]):
                    session_manager.update_session_progress(session_id, 0.95, f"Completing: {line[:60]}...")

                # Error detection
                elif any(keyword in line_lower for keyword in [
                    "error", "failed", "exception", "traceback", "timeout"
                ]):
                    session_manager.update_session_progress(session_id, 0.0, f"Error: {line[:60]}...")

                # General progress update for any output (ensures progress moves)
                else:
                    # Get current progress and increment slightly
                    current_session = session_manager.get_session(session_id)
                    if current_session and current_session.progress < 0.9:
                        new_progress = min(current_session.progress + 0.01, 0.9)
                        session_manager.update_session_progress(session_id, new_progress, f"Processing: {line[:60]}...")

            executor.add_output_callback(session_id, websocket_callback)
            logger.info("WebSocket callback added")

            # Start SWE-Agent execution with enhanced error handling
            session_manager.update_session_progress(session_id, 0.3, "Launching SWE-Agent process...")
            logger.info(f"Starting SWE-Agent for session {session_id}")
            success, error_message = executor.start_agent(session_id, swe_config)

            if success:
                session_manager.update_session_progress(session_id, 0.4, "SWE-Agent process started")
                logger.info(f"SWE-Agent started successfully for session {session_id}")

                # Start a backup progress monitor to prevent stuck sessions
                def backup_progress_monitor():
                    import time
                    import threading

                    def monitor():
                        time.sleep(30)  # Wait 30 seconds

                        # Check if session is still stuck at 40%
                        current_session = session_manager.get_session(session_id)
                        if current_session and current_session.progress <= 0.4:
                            logger.warning(f"Session {session_id} appears stuck, updating progress...")
                            session_manager.update_session_progress(
                                session_id,
                                0.6,
                                "SWE-Agent running - environment setup in progress..."
                            )

                            # Check again after another 30 seconds
                            time.sleep(30)
                            current_session = session_manager.get_session(session_id)
                            if current_session and current_session.progress <= 0.6:
                                session_manager.update_session_progress(
                                    session_id,
                                    0.8,
                                    "SWE-Agent processing - analyzing repository..."
                                )

                    monitor_thread = threading.Thread(target=monitor, daemon=True)
                    monitor_thread.start()

                backup_progress_monitor()

            else:
                session_manager.update_session_status(session_id, SessionStatus.FAILED, f"Failed to start SWE-Agent: {error_message}")
                session_manager.update_session_progress(session_id, 0.0, f"Failed: {error_message}")
                logger.error(f"Failed to start SWE-Agent for session {session_id}: {error_message}")
                return jsonify({"error": f"Failed to start SWE-Agent: {error_message}"}), 500

        except Exception as import_error:
            logger.exception(f"Failed to import or use simple executor for session {session_id}: {import_error}")
            # Fallback to just updating status without actual execution
            session_manager.update_session_progress(session_id, 0.5, f"Running in simulation mode (SWE-Agent import failed: {str(import_error)[:100]})")

        # Emit WebSocket event for real-time updates
        socketio.emit('session_event', {
            'session_id': session_id,
            'event_type': 'session_started',
            'message': 'Session started successfully'
        })

        return jsonify({"status": "success"})

    except Exception as e:
        logger.exception("Error starting session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/stop', methods=['POST'])
def stop_session(session_id):
    """Stop a session using SessionManager and SWE-Agent executor."""
    try:
        from bridge.core.session_manager import session_manager, SessionStatus
        from bridge.core.swe_agent_executor import get_swe_agent_executor

        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404

        # Get SWE-Agent executor and stop the process
        executor = get_swe_agent_executor(session_manager)
        executor.stop_agent(session_id)

        # Update session status to terminated
        success = session_manager.update_session_status(session_id, SessionStatus.TERMINATED)
        if not success:
            return jsonify({"error": "Failed to stop session"}), 500

        # Emit WebSocket event for real-time updates
        socketio.emit('session_event', {
            'session_id': session_id,
            'event_type': 'session_stopped',
            'message': 'SWE-Agent stopped successfully'
        })

        return jsonify({"status": "success"})

    except Exception as e:
        logger.exception("Error stopping session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>', methods=['GET'])
def get_session(session_id):
    """Get session details using SessionManager."""
    try:
        from bridge.core.session_manager import session_manager

        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404

        return jsonify(session.to_dict())

    except Exception as e:
        logger.exception("Error getting session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/trajectory', methods=['GET'])
def get_session_trajectory(session_id):
    """Get session trajectory using SessionManager."""
    try:
        from bridge.core.session_manager import session_manager

        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404

        return jsonify({
            "session_id": session_id,
            "trajectory": session.trajectory,
            "status": "success"
        })

    except Exception as e:
        logger.exception("Error getting session trajectory")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/force-progress', methods=['POST'])
def force_session_progress(session_id):
    """Force update session progress - useful for debugging stuck sessions."""
    try:
        from bridge.core.session_manager import session_manager

        data = request.get_json() or {}
        progress = data.get('progress', 0.7)
        message = data.get('message', 'Manually updated progress')

        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404

        # Update progress
        session_manager.update_session_progress(session_id, progress, message)

        logger.info(f"Manually updated progress for session {session_id} to {progress}: {message}")

        return jsonify({
            "status": "success",
            "session_id": session_id,
            "progress": progress,
            "message": message
        })

    except Exception as e:
        logger.exception("Error forcing session progress")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/start-local', methods=['POST'])
def start_local_session(session_id):
    """Start a SWE-Agent session using local execution for better monitoring."""
    try:
        from bridge.core.session_manager import session_manager, SessionStatus
        from bridge.core.local_swe_executor import get_local_swe_executor, LocalSWEConfig

        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404

        if session.status == SessionStatus.RUNNING:
            return jsonify({"error": "Session is already running"}), 400

        # Update session status
        session_manager.update_session_status(session_id, SessionStatus.RUNNING)
        session_manager.update_session_progress(session_id, 0.1, "Initializing Local SWE-Agent...")

        # Get local executor
        executor = get_local_swe_executor()

        # Create local config from session config with git bypass enabled
        local_config = LocalSWEConfig(
            repo_path=session.config.repo_path,
            problem_statement=session.config.problem_statement,
            model_name=session.config.model_name,
            max_cost=getattr(session.config, 'max_cost', 2.0),
            max_calls=getattr(session.config, 'max_calls', 50),
            timeout=getattr(session.config, 'timeout', 300),
            bypass_git_validation=True,  # Enable git bypass
            use_repo_copy=True          # Use clean repository copies
        )

        # Add WebSocket output callback for local execution
        def local_websocket_callback(session_id: str, line: str, stream: str):
            from datetime import datetime

            # Log all output for debugging
            logger.info(f"Local SWE-Agent output [{session_id}] ({stream}): {line}")

            # Emit output to WebSocket
            socketio.emit('session_output', {
                'session_id': session_id,
                'output': line,
                'stream': stream,
                'timestamp': datetime.now().isoformat()
            }, room=f"output_{session_id}")

            # Enhanced progress tracking for local execution
            line_lower = line.lower()

            # Initialization phase (10-40%)
            if any(keyword in line_lower for keyword in [
                "starting", "initializing", "loading", "setting up"
            ]):
                session_manager.update_session_progress(session_id, 0.3, f"Initializing: {line[:60]}...")

            # Analysis phase (40-70%)
            elif any(keyword in line_lower for keyword in [
                "exploring", "reading", "analyzing", "processing", "examining"
            ]):
                session_manager.update_session_progress(session_id, 0.6, f"Analyzing: {line[:60]}...")

            # Execution phase (70-90%)
            elif any(keyword in line_lower for keyword in [
                "executing", "running", "command", "step", "action"
            ]):
                session_manager.update_session_progress(session_id, 0.8, f"Executing: {line[:60]}...")

            # Completion phase (90-100%)
            elif any(keyword in line_lower for keyword in [
                "completed", "finished", "done", "success", "final"
            ]):
                session_manager.update_session_progress(session_id, 0.95, f"Completing: {line[:60]}...")

            # Error detection
            elif any(keyword in line_lower for keyword in [
                "error", "failed", "exception", "traceback", "timeout"
            ]):
                session_manager.update_session_progress(session_id, 0.0, f"Error: {line[:60]}...")

            # Progress updates
            elif "[progress]" in line_lower or "[info]" in line_lower:
                current_session = session_manager.get_session(session_id)
                if current_session and current_session.progress < 0.9:
                    new_progress = min(current_session.progress + 0.05, 0.9)
                    session_manager.update_session_progress(session_id, new_progress, f"Progress: {line[:60]}...")

        executor.add_output_callback(session_id, local_websocket_callback)

        # Start local SWE-Agent
        success, error_message = executor.start_agent(session_id, local_config)

        if success:
            session_manager.update_session_progress(session_id, 0.4, "Local SWE-Agent process started")
            logger.info(f"Local SWE-Agent started successfully for session {session_id}")

        else:
            session_manager.update_session_status(session_id, SessionStatus.FAILED, f"Failed to start Local SWE-Agent: {error_message}")
            session_manager.update_session_progress(session_id, 0.0, f"Failed: {error_message}")
            logger.error(f"Failed to start Local SWE-Agent for session {session_id}: {error_message}")
            return jsonify({"error": f"Failed to start Local SWE-Agent: {error_message}"}), 500

        return jsonify({
            "status": "success",
            "message": "Local SWE-Agent session started successfully",
            "session_id": session_id,
            "execution_type": "local"
        })

    except Exception as e:
        logger.exception("Error starting local session")
        return jsonify({"error": str(e)}), 500


@socketio.on('connect')
def handle_connect():
    """Handle client connection."""
    logger.info("Client connected")


@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection."""
    logger.info("Client disconnected")


@socketio.on('subscribe_session')
def handle_subscribe_session(data):
    """Handle session subscription for real-time updates."""
    try:
        session_id = data.get('session_id')
        if not session_id:
            emit('error', {"error": "No session_id provided"})
            return

        # Join room for this session
        from flask_socketio import join_room
        join_room(session_id)

        logger.info(f"Client subscribed to session {session_id}")
        emit('subscribed', {
            "session_id": session_id,
            "message": "Subscribed to session updates"
        })

    except Exception as e:
        logger.error(f"Error in session subscription: {e}")
        emit('error', {"error": str(e)})


@socketio.on('unsubscribe_session')
def handle_unsubscribe_session(data):
    """Handle session unsubscription."""
    try:
        session_id = data.get('session_id')
        if not session_id:
            emit('error', {"error": "No session_id provided"})
            return

        # Leave room for this session
        from flask_socketio import leave_room
        leave_room(session_id)

        logger.info(f"Client unsubscribed from session {session_id}")
        emit('unsubscribed', {
            "session_id": session_id,
            "message": "Unsubscribed from session updates"
        })

    except Exception as e:
        logger.error(f"Error in session unsubscription: {e}")
        emit('error', {"error": str(e)})


@socketio.on('subscribe_output')
def handle_subscribe_output(data):
    """Handle subscription to real-time SWE-Agent output."""
    try:
        session_id = data.get('session_id')
        if not session_id:
            emit('error', {"error": "No session_id provided"})
            return

        # Join room for this session's output
        from flask_socketio import join_room
        join_room(f"output_{session_id}")

        logger.info(f"Client subscribed to output for session {session_id}")
        emit('output_subscribed', {
            "session_id": session_id,
            "message": "Subscribed to real-time output"
        })

    except Exception as e:
        logger.error(f"Error in output subscription: {e}")
        emit('error', {"error": str(e)})


@socketio.on('unsubscribe_output')
def handle_unsubscribe_output(data):
    """Handle unsubscription from real-time SWE-Agent output."""
    try:
        session_id = data.get('session_id')
        if not session_id:
            emit('error', {"error": "No session_id provided"})
            return

        # Leave room for this session's output
        from flask_socketio import leave_room
        leave_room(f"output_{session_id}")

        logger.info(f"Client unsubscribed from output for session {session_id}")
        emit('output_unsubscribed', {
            "session_id": session_id,
            "message": "Unsubscribed from real-time output"
        })

    except Exception as e:
        logger.error(f"Error in output unsubscription: {e}")
        emit('error', {"error": str(e)})


def run_server(debug=False):
    """Run the API server."""
    host = config.get("bridge", "api_host", default="localhost")
    port = config.get("bridge", "api_port", default=8080)

    logger.info(
        f"Starting Enhanced API server on {host}:{port}",
        host=host,
        port=port,
        debug=debug
    )

    # Log startup information
    logger.info("Enhanced logging system initialized")
    logger.info("Performance monitoring enabled")
    logger.info("Request tracking middleware active")

    # Audit log for server startup
    audit_logger.info(
        "API server startup",
        host=host,
        port=port,
        debug=debug
    )

    # Use different settings based on whether we're in main thread or not
    is_main_thread = threading.current_thread() is threading.main_thread()

    if is_main_thread:
        # Running in main thread - can use debug mode
        logger.info("Running in main thread with debug support")
        socketio.run(app, host=host, port=port, debug=debug, allow_unsafe_werkzeug=True)
    else:
        # Running in thread - disable debug mode to avoid signal issues
        logger.info("Running in background thread")
        socketio.run(app, host=host, port=port, debug=False, allow_unsafe_werkzeug=True, use_reloader=False)


if __name__ == "__main__":
    run_server()
