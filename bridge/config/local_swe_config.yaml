# Local SWE-Agent configuration for bridge integration
# This config runs SWE-Agent locally without <PERSON><PERSON> for better monitoring

agent:
  templates:
    system_template: |-
      You are a helpful AI assistant that can analyze and explain codebases.
      You have access to various tools to explore, read, and understand code repositories.
    instance_template: |-
      I need you to analyze the codebase in the directory {{working_dir}}.
      
      Problem statement: {{problem_statement}}
      
      Please help me understand this codebase by:
      1. First, explore the repository structure to get an overview
      2. Read key files to understand the main functionality
      3. Provide a clear explanation of what this codebase does
      4. Identify the main components and their relationships
      
      Your analysis should be thorough and helpful for understanding the project.
    next_step_template: |-
      OBSERVATION:
      {{observation}}
    next_step_no_output_template: |-
      Your command ran successfully and did not produce any output.
  
  tools:
    bundles:
      - path: tools/search
      - path: tools/edit_anthropic
      - path: tools/submit
    enable_bash_tool: true
    parse_function:
      type: function_calling
    execution_timeout: 30
    max_consecutive_execution_timeouts: 5
    total_execution_timeout: 0
  
  model:
    name: claude-sonnet-4-20250514
    per_instance_cost_limit: 2.00
    total_cost_limit: 10.00
    per_instance_call_limit: 50
    max_input_tokens: 0

env:
  deployment:
    type: local  # Run locally instead of Docker
  repo:
    type: local
  post_startup_commands: []
  post_startup_command_timeout: 30

# Default problem statement for testing
problem_statement:
  text: "Analyze and explain this codebase"
