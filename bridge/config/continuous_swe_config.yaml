# Continuous Execution Configuration for SWE-Agent
# This configuration enables unlimited execution time and better timeout handling

agent:
  type: default
  model:
    name: claude-sonnet-4-20250514
    api_key: $ANTHROPIC_API_KEY
    per_instance_cost_limit: 50.0  # Higher cost limit for long tasks
    per_instance_call_limit: 500   # More API calls allowed
    total_cost_limit: 500.0
    temperature: 0.0
    delay: 1.0
  
  tools:
    # Extended timeout configurations
    execution_timeout: 600          # 10 minutes per command (was 30 seconds)
    install_timeout: 1200           # 20 minutes for installations (was 5 minutes)
    total_execution_timeout: 0      # Unlimited total execution time (was 30 minutes)
    max_consecutive_execution_timeouts: 10  # More tolerance (was 3)
    
    # Tool bundles for comprehensive functionality
    bundles:
      - registry
      - windowed
      - search
      - windowed_edit_linting
      - submit
    
    env_variables:
      WINDOW: 200
      OVERLAP: 5
      CONTINUOUS_MODE: "true"
      AUTO_RESUME: "true"
  
  # Unlimited iterations for continuous execution
  max_iterations: 0  # 0 means unlimited
  max_requeries: 10  # More retries on errors
  
  templates:
    system_template: |
      You are a software engineering AI assistant working in continuous mode.
      You have unlimited time and iterations to complete complex tasks.
      Break down large problems into smaller, manageable steps.
      Use the available tools to analyze, modify, and test code systematically.
      If you encounter timeouts or errors, retry with different approaches.
      Continue working until the task is fully completed or you determine it's impossible.
      
      Available tools: {command_docs}
      
      Current working directory: {working_dir}
      Repository: {repo_path}
      
      Task: {problem_statement}
      
      Work systematically and thoroughly. You have unlimited time to complete this task.

environment:
  deployment:
    type: docker
    image: python:3.11
    memory_limit: 8GB
    timeout: 0  # Unlimited deployment timeout
    
  # Extended timeouts for environment operations
  post_startup_command_timeout: 1200  # 20 minutes
  
  # Environment variables for continuous mode
  env_variables:
    PYTHONUNBUFFERED: "1"
    CONTINUOUS_EXECUTION: "true"
    AUTO_RECOVERY: "true"

# Retry configuration for better error handling
retry:
  max_attempts: 10
  backoff_factor: 1.2
  circuit_breaker_enabled: true
  circuit_breaker_threshold: 15
  retry_on_errors:
    - timeout
    - connection_error
    - rate_limit
    - server_error
    - command_timeout

# Session persistence for long-running tasks
session:
  persistence_enabled: true
  auto_save_interval: 600  # Save every 10 minutes
  max_session_duration: 0  # Unlimited
  cleanup_on_exit: false
  heartbeat_interval: 300  # 5 minutes

# Parallel execution support
parallel:
  enabled: true
  max_workers: 4
  task_queue_size: 100
  worker_timeout: 3600  # 1 hour per worker task

# Enhanced logging for debugging
logging:
  level: INFO
  enable_debug_logging: true
  log_trajectory: true
  log_tool_usage: true
  log_model_calls: true

# Monitoring and metrics
monitoring:
  enable_metrics: true
  enable_tracing: true
  performance_monitoring: true
  memory_monitoring: true
  
# Security settings (relaxed for continuous mode)
security:
  enable_auth: false  # Disable for continuous execution
  rate_limit_per_minute: 10000  # Higher rate limit
  max_file_size_mb: 100  # Larger file size limit
