#!/usr/bin/env python3
"""
Test script to validate SWE-Agent integration with the bridge.
This script tests the complete integration pipeline.
"""

import os
import sys
import time
import json
import requests
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_bridge_server_health():
    """Test if the bridge server is running."""
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ Bridge server is running")
            return True
        else:
            print(f"❌ Bridge server health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Bridge server is not accessible: {e}")
        return False

def test_session_creation():
    """Test creating a new session."""
    try:
        session_data = {
            "problem_statement": "List all Python files in the current directory and show their sizes",
            "repo_path": str(project_root),
            "model_name": "claude-sonnet-4-20250514"
        }
        
        response = requests.post(
            "http://localhost:8080/api/sessions",
            json=session_data,
            timeout=10
        )
        
        if response.status_code == 201:
            session_info = response.json()
            session_id = session_info.get("session_id")
            print(f"✅ Session created successfully: {session_id}")
            return session_id
        else:
            print(f"❌ Session creation failed: {response.status_code} - {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Session creation request failed: {e}")
        return None

def test_session_start(session_id):
    """Test starting a session with SWE-Agent execution."""
    try:
        response = requests.post(
            f"http://localhost:8080/api/sessions/{session_id}/start",
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ Session started successfully")
            return True
        else:
            print(f"❌ Session start failed: {response.status_code} - {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Session start request failed: {e}")
        return False

def test_session_monitoring(session_id, max_wait_time=300):
    """Monitor session progress and output."""
    print(f"🔍 Monitoring session {session_id} for up to {max_wait_time} seconds...")
    
    start_time = time.time()
    last_status = None
    
    while time.time() - start_time < max_wait_time:
        try:
            # Get session status
            response = requests.get(
                f"http://localhost:8080/api/sessions/{session_id}",
                timeout=5
            )
            
            if response.status_code == 200:
                session_info = response.json()
                status = session_info.get("status")
                progress = session_info.get("progress", 0)
                current_step = session_info.get("current_step", "")
                
                if status != last_status:
                    print(f"📊 Status: {status}, Progress: {progress:.1%}, Step: {current_step}")
                    last_status = status
                
                # Check if session is complete
                if status in ["completed", "failed", "terminated"]:
                    print(f"🏁 Session finished with status: {status}")
                    
                    # Get trajectory
                    traj_response = requests.get(
                        f"http://localhost:8080/api/sessions/{session_id}/trajectory",
                        timeout=5
                    )
                    
                    if traj_response.status_code == 200:
                        trajectory = traj_response.json().get("trajectory", [])
                        print(f"📝 Trajectory has {len(trajectory)} steps")
                        
                        # Show last few steps
                        for step in trajectory[-3:]:
                            step_type = step.get("type", "unknown")
                            content = step.get("content", step.get("data", ""))
                            if isinstance(content, dict):
                                content = json.dumps(content, indent=2)
                            print(f"   {step_type}: {content[:100]}...")
                    
                    return status == "completed"
                    
            else:
                print(f"❌ Failed to get session status: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Session monitoring request failed: {e}")
            
        time.sleep(5)  # Wait 5 seconds between checks
    
    print(f"⏰ Monitoring timeout after {max_wait_time} seconds")
    return False

def test_continuous_session():
    """Test creating and running a continuous session."""
    try:
        session_data = {
            "problem_statement": "Analyze the bridge codebase and create a summary of all Python files, their purposes, and key functions. This is a complex task that should run continuously.",
            "repo_path": str(project_root),
            "model_name": "claude-sonnet-4-20250514",
            "enable_continuous": True
        }
        
        response = requests.post(
            "http://localhost:8080/api/sessions/continuous",
            json=session_data,
            timeout=10
        )
        
        if response.status_code == 201:
            session_info = response.json()
            session_id = session_info.get("session_id")
            print(f"✅ Continuous session created: {session_id}")
            return session_id
        else:
            print(f"❌ Continuous session creation failed: {response.status_code} - {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Continuous session request failed: {e}")
        return None

def test_swe_agent_direct():
    """Test SWE-Agent directly to ensure it's working."""
    try:
        import subprocess
        
        # Test SWE-Agent CLI
        result = subprocess.run([
            str(project_root / "swe_venv" / "bin" / "python"),
            "-m", "sweagent", "--help"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ SWE-Agent CLI is working")
            return True
        else:
            print(f"❌ SWE-Agent CLI failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ SWE-Agent direct test failed: {e}")
        return False

def main():
    """Run all validation tests."""
    print("🚀 Starting SWE-Agent Integration Validation")
    print("=" * 50)
    
    # Test 1: Bridge server health
    if not test_bridge_server_health():
        print("❌ Bridge server is not running. Please start it first.")
        return False
    
    # Test 2: SWE-Agent direct access
    if not test_swe_agent_direct():
        print("❌ SWE-Agent is not properly installed or accessible.")
        return False
    
    # Test 3: Session creation and execution
    session_id = test_session_creation()
    if not session_id:
        print("❌ Session creation failed.")
        return False
    
    # Test 4: Session start with SWE-Agent
    if not test_session_start(session_id):
        print("❌ Session start failed.")
        return False
    
    # Test 5: Monitor session execution
    success = test_session_monitoring(session_id, max_wait_time=180)  # 3 minutes
    
    if success:
        print("✅ All tests passed! SWE-Agent integration is working.")
    else:
        print("⚠️  Session did not complete successfully, but integration appears to be working.")
    
    # Test 6: Continuous session (optional)
    print("\n🔄 Testing continuous session...")
    continuous_session_id = test_continuous_session()
    if continuous_session_id:
        print(f"✅ Continuous session created: {continuous_session_id}")
        print("   (Monitor this session separately for long-running behavior)")
    
    print("\n🎉 Integration validation complete!")
    return True

if __name__ == "__main__":
    main()
