#!/usr/bin/env python3
"""
Recovery tool for stuck SWE-Agent sessions.
"""

import requests
import json
import sys
import time
import subprocess

def get_stuck_sessions():
    """Find sessions that are stuck at low progress for too long."""
    try:
        response = requests.get("http://localhost:8080/api/sessions")
        if response.status_code == 200:
            sessions = response.json().get("sessions", [])
            
            stuck_sessions = []
            current_time = time.time()
            
            for session in sessions:
                if session['status'] == 'running':
                    # Parse creation time
                    created_at = session['created_at']
                    # Simple time check - if running for more than 10 minutes with low progress
                    if session['progress'] <= 0.4:
                        stuck_sessions.append(session)
                        
            return stuck_sessions
        else:
            print(f"Failed to get sessions: {response.status_code}")
            return []
    except Exception as e:
        print(f"Error getting sessions: {e}")
        return []

def check_docker_logs(session_id):
    """Check Docker container logs for a session."""
    try:
        # Find containers that might be related to this session
        result = subprocess.run(
            ["docker", "ps", "--format", "{{.Names}}"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            container_names = result.stdout.strip().split('\n')
            
            for container_name in container_names:
                if 'python3.11' in container_name:
                    print(f"\n=== Logs for container {container_name} ===")
                    
                    # Get recent logs
                    log_result = subprocess.run(
                        ["docker", "logs", "--tail", "20", container_name],
                        capture_output=True,
                        text=True
                    )
                    
                    if log_result.returncode == 0:
                        print("STDOUT:")
                        print(log_result.stdout)
                        if log_result.stderr:
                            print("STDERR:")
                            print(log_result.stderr)
                    else:
                        print(f"Failed to get logs: {log_result.stderr}")
                        
    except Exception as e:
        print(f"Error checking Docker logs: {e}")

def restart_session(session_id):
    """Attempt to restart a stuck session."""
    try:
        print(f"Attempting to restart session {session_id}...")
        
        # First, try to stop the session
        stop_response = requests.post(f"http://localhost:8080/api/sessions/{session_id}/stop")
        if stop_response.status_code == 200:
            print("Session stopped successfully")
        else:
            print(f"Failed to stop session: {stop_response.status_code}")
            
        time.sleep(2)
        
        # Then try to start it again
        start_response = requests.post(f"http://localhost:8080/api/sessions/{session_id}/start")
        if start_response.status_code == 200:
            print("Session restarted successfully")
            return True
        else:
            print(f"Failed to restart session: {start_response.status_code}")
            print(start_response.text)
            return False
            
    except Exception as e:
        print(f"Error restarting session: {e}")
        return False

def force_progress_update(session_id):
    """Force a progress update by simulating SWE-Agent output."""
    try:
        # Import the session manager and update progress manually
        sys.path.append('/u/Arun Dev/Python Projects/AI-Coding-Agent')
        
        from bridge.core.session_manager import session_manager
        
        # Update progress to indicate we're investigating
        session_manager.update_session_progress(
            session_id, 
            0.6, 
            "Investigating stuck session - checking Docker containers..."
        )
        
        print(f"Updated progress for session {session_id}")
        return True
        
    except Exception as e:
        print(f"Error updating progress: {e}")
        return False

def diagnose_session(session_id):
    """Comprehensive diagnosis of a stuck session."""
    print(f"\n=== Diagnosing session {session_id} ===")
    
    # Get session details
    try:
        response = requests.get(f"http://localhost:8080/api/sessions/{session_id}")
        if response.status_code == 200:
            session = response.json()
            print(f"Session status: {session['status']}")
            print(f"Progress: {session['progress']}")
            print(f"Current step: {session['current_step']}")
            print(f"Repository: {session['config']['repo_path']}")
            print(f"Problem: {session['config']['problem_statement']}")
        else:
            print(f"Failed to get session details: {response.status_code}")
            return
    except Exception as e:
        print(f"Error getting session details: {e}")
        return
    
    # Check Docker logs
    check_docker_logs(session_id)
    
    # Check if we can access the simple executor
    try:
        sys.path.append('/u/Arun Dev/Python Projects/AI-Coding-Agent')
        from bridge.core.simple_swe_executor import get_simple_swe_executor
        
        executor = get_simple_swe_executor()
        print(f"\nExecutor status:")
        print(f"Active processes: {len(executor.active_processes)}")
        print(f"Process threads: {len(executor.process_threads)}")
        
        # Check if this session has an active process
        if session_id in executor.active_processes:
            process = executor.active_processes[session_id]
            print(f"Process for session {session_id}: running = {process.poll() is None}")
        else:
            print(f"No active process found for session {session_id}")
            
    except Exception as e:
        print(f"Error checking executor: {e}")

def main():
    print("=== SWE-Agent Session Recovery Tool ===")
    
    # Find stuck sessions
    stuck_sessions = get_stuck_sessions()
    
    if not stuck_sessions:
        print("No stuck sessions found.")
        return
    
    print(f"Found {len(stuck_sessions)} potentially stuck sessions:")
    
    for i, session in enumerate(stuck_sessions):
        print(f"\n{i+1}. Session {session['session_id']}")
        print(f"   Progress: {session['progress']}")
        print(f"   Step: {session['current_step']}")
        print(f"   Created: {session['created_at']}")
        
    # Diagnose each stuck session
    for session in stuck_sessions:
        session_id = session['session_id']
        diagnose_session(session_id)
        
        # Ask user what to do
        print(f"\nOptions for session {session_id}:")
        print("1. Force progress update")
        print("2. Restart session")
        print("3. Skip")
        
        try:
            choice = input("Choose option (1-3): ").strip()
            
            if choice == "1":
                force_progress_update(session_id)
            elif choice == "2":
                restart_session(session_id)
            elif choice == "3":
                print("Skipping...")
            else:
                print("Invalid choice, skipping...")
                
        except KeyboardInterrupt:
            print("\nExiting...")
            break

if __name__ == "__main__":
    main()
