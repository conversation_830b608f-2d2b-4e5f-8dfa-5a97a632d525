# Enhanced Logging System

The AI Coding Agent Bridge features a comprehensive, component-based logging system designed for production monitoring, debugging, and performance analysis.

## 🎯 Overview

### Key Features
- **Component-Based Logging**: Separate loggers for different system components
- **Multiple Output Formats**: Console, structured JSON, and component-specific logs
- **Automatic Log Rotation**: Size-based rotation with configurable retention
- **Performance Monitoring**: Integrated performance metrics and request tracking
- **Contextual Logging**: Request IDs, session IDs, and rich context data
- **Real-time Monitoring**: Live log streaming and metrics APIs

## 📁 Log File Structure

```
logs/
├── bridge.log                 # Main application log
├── bridge_structured.json     # Structured JSON logs
├── api.log                    # API component logs
├── chat.log                   # Chat system logs
├── swe_agent.log              # SWE-Agent integration logs
├── session.log                # Session management logs
├── auth.log                   # Authentication logs
├── workspace.log              # File operations logs
├── terminal.log               # Terminal integration logs
├── security.log               # Security events
├── performance.log            # Performance metrics
├── audit.log                  # Audit trail
└── errors.log                 # Error-only logs
```

## 🔧 Configuration

### Environment Variables
```bash
# Logging level (TRACE, DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log file location
LOG_FILE=logs/bridge.log

# Enable/disable specific log types
ENABLE_PERFORMANCE_LOGGING=true
ENABLE_AUDIT_LOGGING=true
ENABLE_STRUCTURED_LOGGING=true
```

### Configuration File (config.yaml)
```yaml
logging:
  level: INFO
  file: logs/bridge.log
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  max_size: 10MB
  backup_count: 5
  
  # Component-specific settings
  components:
    api:
      level: INFO
      file: logs/api.log
    chat:
      level: DEBUG
      file: logs/chat.log
    performance:
      level: INFO
      file: logs/performance.log
```

## 📊 Log Formats

### Standard Format
```
2025-06-06 11:44:01,375 - bridge.api.api_server - INFO - Request started: GET /health
```

### Structured JSON Format
```json
{
  "timestamp": "2025-06-06T11:44:01.375000",
  "level": "INFO",
  "component": "api",
  "module": "bridge.api.api_server",
  "message": "Request started: GET /health",
  "context": {
    "request_id": "req-123",
    "method": "GET",
    "path": "/health",
    "remote_addr": "127.0.0.1"
  },
  "request_id": "req-123",
  "session_id": null,
  "user_id": null
}
```

## 🏷️ Log Components

### API Component (`api.log`)
Tracks all API requests and responses:
```
2025-06-06 11:44:01,375 - bridge.api.api_server - INFO - Request started: GET /health
2025-06-06 11:44:01,376 - bridge.api.api_server - INFO - Request completed: GET /health - 200 in 0.002s
```

### Chat Component (`chat.log`)
Monitors chat interactions:
```
2025-06-06 11:44:02,123 - bridge.api.chat_endpoints - INFO - Chat message received for session sess-456
2025-06-06 11:44:02,456 - bridge.api.chat_endpoints - INFO - Chat response generated for session sess-456
```

### Performance Component (`performance.log`)
System and request performance metrics:
```
2025-06-06 11:44:01,376 - bridge.core.performance_monitor - INFO - Performance metric: request_duration = 0.001 seconds
2025-06-06 11:44:01,407 - bridge.core.performance_monitor - INFO - Performance metric: memory_usage = 69.4 percent
```

### Audit Component (`audit.log`)
Security and compliance events:
```
2025-06-06 11:44:01,500 - bridge.api.api_server - INFO - API server startup
2025-06-06 11:44:02,100 - bridge.api.chat_endpoints - INFO - Chat message sent
```

## 📈 Performance Monitoring

### Real-time Metrics
The system automatically tracks:
- **System Resources**: CPU usage, memory consumption
- **Request Performance**: Response times, throughput
- **Error Rates**: Success/failure ratios
- **Active Sessions**: Concurrent user sessions

### Metrics API
Access real-time metrics via REST API:
```bash
# Get performance metrics
curl http://localhost:8080/api/performance

# Get system health
curl http://localhost:8080/health
```

### Performance Logs
Detailed performance data in `performance.log`:
```
2025-06-06 11:44:01,376 - bridge.core.performance_monitor - INFO - Request req-123 completed in 0.001s
2025-06-06 11:44:01,407 - bridge.core.performance_monitor - INFO - System metric: memory_usage = 69.4%
2025-06-06 11:44:01,408 - bridge.core.performance_monitor - INFO - System metric: cpu_usage = 6.4%
```

## 🔍 Log Analysis

### Viewing Logs

**Real-time monitoring:**
```bash
# Main application log
tail -f logs/bridge.log

# API requests only
tail -f logs/api.log

# Performance metrics
tail -f logs/performance.log

# Errors only
tail -f logs/errors.log
```

**Structured log analysis:**
```bash
# Parse JSON logs with jq
tail -f logs/bridge_structured.json | jq .

# Filter by component
tail -f logs/bridge_structured.json | jq 'select(.component == "api")'

# Filter by log level
tail -f logs/bridge_structured.json | jq 'select(.level == "ERROR")'
```

### Log Rotation

Automatic rotation based on file size:
- **Max Size**: 10MB (configurable)
- **Backup Count**: 5 files (configurable)
- **Naming**: `bridge.log.1`, `bridge.log.2`, etc.

Manual rotation:
```bash
# Force log rotation
kill -USR1 $(pgrep -f "python start_api_server.py")
```

## 🚨 Error Tracking

### Error Logs (`errors.log`)
All errors automatically logged with full context:
```
2025-06-06 11:44:05,123 - bridge.api.chat_endpoints - ERROR - Error sending chat message
Traceback (most recent call last):
  File "bridge/api/chat_endpoints.py", line 175, in send_message
    response = chat_manager.send_message(session_id, message, context)
ValueError: Invalid session ID
```

### Error Context
Errors include rich contextual information:
- Request ID for tracing
- Session ID if applicable
- User ID for audit trails
- Full stack traces
- Request parameters

## 📊 Monitoring Integration

### Prometheus Metrics (Future)
Planned integration with Prometheus:
```python
# Example metrics
http_requests_total{method="GET", endpoint="/health", status="200"}
request_duration_seconds{endpoint="/api/chat"}
system_memory_usage_percent
```

### Log Aggregation
Compatible with popular log aggregation tools:
- **ELK Stack**: Elasticsearch, Logstash, Kibana
- **Fluentd**: Log collection and forwarding
- **Grafana**: Visualization and alerting

## 🔧 Development Usage

### Custom Logging
```python
from bridge.core.enhanced_logging import get_api_logger

logger = get_api_logger(__name__)

# Basic logging
logger.info("Operation completed")

# Contextual logging
logger.info(
    "User action performed",
    request_id="req-123",
    user_id="user-456",
    context={"action": "file_upload", "size": 1024}
)

# Error logging with context
try:
    risky_operation()
except Exception as e:
    logger.exception(
        "Operation failed",
        request_id="req-123",
        context={"operation": "file_processing"}
    )
```

### Performance Tracking
```python
from bridge.core.performance_monitor import track_performance, log_performance_metric

# Decorator for automatic tracking
@track_performance("/api/custom", "POST")
def my_function():
    # Function implementation
    pass

# Manual performance logging
log_performance_metric("custom_metric", 42.5, "units", {"context": "test"})
```

## 🛠️ Troubleshooting

### Common Issues

**Logs not appearing:**
```bash
# Check log directory permissions
ls -la logs/

# Verify log level configuration
grep LOG_LEVEL .env

# Check disk space
df -h
```

**Performance issues:**
```bash
# Check log file sizes
du -sh logs/*

# Monitor real-time performance
curl http://localhost:8080/api/performance
```

**Log rotation not working:**
```bash
# Check log rotation configuration
grep -A 5 "RotatingFileHandler" bridge/core/enhanced_logging.py

# Manual cleanup
find logs/ -name "*.log.*" -mtime +7 -delete
```

### Debug Mode
Enable debug logging for detailed troubleshooting:
```bash
LOG_LEVEL=DEBUG python start_api_server.py
```

## 📋 Best Practices

### Production Deployment
1. **Set appropriate log levels** (INFO or WARNING for production)
2. **Configure log rotation** to prevent disk space issues
3. **Monitor log file sizes** and system resources
4. **Set up log aggregation** for centralized monitoring
5. **Configure alerting** for error rate thresholds

### Security Considerations
1. **Sanitize sensitive data** before logging
2. **Restrict log file access** with proper permissions
3. **Audit log access** and modifications
4. **Encrypt logs** for compliance requirements

### Performance Optimization
1. **Use appropriate log levels** to reduce I/O
2. **Configure async logging** for high-throughput scenarios
3. **Monitor logging overhead** in performance metrics
4. **Implement log sampling** for very high-volume events

---

*For performance monitoring details, see [Performance Monitoring](PERFORMANCE.md)*
*For deployment considerations, see [Deployment Guide](DEPLOYMENT.md)*
