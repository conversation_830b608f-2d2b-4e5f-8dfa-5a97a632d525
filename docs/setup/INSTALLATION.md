# Installation Guide

This guide provides step-by-step instructions for installing and setting up the AI Coding Agent Bridge system.

## 📋 Prerequisites

### System Requirements
- **Python**: 3.8 or higher (3.11+ recommended for SWE-Agent)
- **Node.js**: 16.x or higher (for VS Code extension development)
- **Git**: Latest version
- **Operating System**: Linux, macOS, or Windows with WSL

### Hardware Requirements
- **RAM**: Minimum 8GB, recommended 16GB+
- **Storage**: At least 5GB free space
- **CPU**: Multi-core processor recommended

## 🚀 Quick Installation

### 1. Clone the Repository
```bash
git clone https://github.com/your-org/AI-Coding-Agent.git
cd AI-Coding-Agent
```

### 2. Set Up Python Environment
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Install Additional Dependencies
```bash
# Install psutil for performance monitoring
pip install psutil

# Install development dependencies (optional)
pip install -r requirements-dev.txt
```

### 4. Configure Environment Variables
```bash
# Copy example environment file
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

### 5. Start the Bridge Server
```bash
python start_api_server.py
```

The server will be available at `http://localhost:8080`

## 🔧 Detailed Installation

### Python Environment Setup

#### Option 1: Using venv (Recommended)
```bash
python -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

#### Option 2: Using conda
```bash
conda create -n ai-coding-agent python=3.11
conda activate ai-coding-agent
pip install -r requirements.txt
```

### SWE-Agent Setup (Optional)

For full SWE-Agent functionality:

```bash
# Create Python 3.11 environment for SWE-Agent
python3.11 -m venv swe_venv
source swe_venv/bin/activate
pip install -e ./swe-agent
```

### VS Code Extension Installation

1. Open VS Code
2. Go to Extensions (Ctrl+Shift+X)
3. Install from VSIX:
   ```bash
   code --install-extension vscode-extension/ai-coding-agent-0.2.0.vsix
   ```

### Vim Extension Installation

Add to your `.vimrc`:
```vim
" Add the vim-extension directory to your runtime path
set runtimepath+=path/to/AI-Coding-Agent/vim-extension

" Enable the plugin
let g:augment_enabled = 1
let g:augment_api_url = 'http://localhost:8080'
```

## 🔑 Configuration

### Environment Variables

Create a `.env` file in the project root:

```bash
# API Configuration
BRIDGE_API_PORT=8080
BRIDGE_API_HOST=localhost

# SWE-Agent Configuration
SWE_AGENT_PORT=8000
SWE_AGENT_MODEL=claude-sonnet-4-20250514

# Logging Configuration
LOG_LEVEL=INFO

# API Keys (required for AI functionality)
ANTHROPIC_API_KEY=your_anthropic_key_here
OPENAI_API_KEY=your_openai_key_here

# OAuth Configuration (optional)
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
```

### Configuration File

Create `config.yaml` for advanced configuration:

```yaml
bridge:
  api_host: localhost
  api_port: 8080
  vim_port: 8081

swe_agent:
  api_host: localhost
  api_port: 8000
  model: claude-sonnet-4-20250514

logging:
  level: INFO
  file: logs/bridge.log

security:
  rate_limit_per_minute: 1000
  max_file_size_mb: 10
  allowed_file_extensions:
    - .py
    - .js
    - .ts
    - .java
    - .cpp
    - .c
    - .h
    - .hpp
```

## ✅ Verification

### 1. Test Basic Functionality
```bash
# Test the API server
curl http://localhost:8080/health

# Expected response:
# {"status":"ok","timestamp":...,"system":{...}}
```

### 2. Test Performance Monitoring
```bash
curl http://localhost:8080/api/performance
```

### 3. Test SWE-Agent Integration
```bash
python -c "
from bridge.integrations.swe_tool_proxy import swe_tool_proxy
print('Available tools:', len(swe_tool_proxy.available_tools))
"
```

### 4. Run Test Suite
```bash
# Run basic tests
python -m pytest tests/ -v

# Run integration tests
python test_bridge.py
```

## 🐛 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Find process using port 8080
lsof -i :8080

# Kill the process
kill -9 <PID>
```

#### Python Version Issues
```bash
# Check Python version
python --version

# Use specific Python version
python3.11 -m venv venv
```

#### Missing Dependencies
```bash
# Reinstall dependencies
pip install --force-reinstall -r requirements.txt

# Install specific missing package
pip install psutil flask flask-cors flask-socketio
```

#### Permission Issues
```bash
# Fix permissions on Linux/macOS
chmod +x start_bridge.sh
chmod +x activate_venv.sh
```

### Log Analysis

Check logs for detailed error information:
```bash
# View main log
tail -f logs/bridge.log

# View error-only log
tail -f logs/errors.log

# View performance log
tail -f logs/performance.log
```

## 🔄 Updates

### Updating the System
```bash
# Pull latest changes
git pull origin main

# Update dependencies
pip install --upgrade -r requirements.txt

# Restart services
python start_api_server.py
```

### Database Migrations (if applicable)
```bash
# Run any pending migrations
python manage.py migrate
```

## 🚀 Next Steps

After successful installation:

1. Read the [Quick Start Guide](QUICK_START.md)
2. Configure your [Development Environment](../development/SETUP.md)
3. Explore the [API Documentation](../api/REST_API.md)
4. Set up [VS Code Integration](../integrations/VSCODE.md)

## 📞 Support

If you encounter issues:

1. Check the [Troubleshooting Guide](TROUBLESHOOTING.md)
2. Review the [FAQ](../reference/FAQ.md)
3. Search existing [GitHub Issues](https://github.com/your-org/AI-Coding-Agent/issues)
4. Create a new issue with detailed information

---

*For more detailed configuration options, see the [Configuration Guide](CONFIGURATION.md)*
