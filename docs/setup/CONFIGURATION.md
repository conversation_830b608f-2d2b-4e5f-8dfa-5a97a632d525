# Configuration Guide

This guide covers all configuration options for the AI Coding Agent Bridge system.

## 📋 Configuration Overview

The system supports multiple configuration methods with the following precedence (highest to lowest):

1. **Command Line Arguments** - Runtime overrides
2. **Environment Variables** - Runtime configuration
3. **Configuration Files** - Persistent settings
4. **Default Values** - Built-in defaults

## 🔧 Environment Variables

### Core Configuration

Create a `.env` file in the project root:

```bash
# API Server Configuration
BRIDGE_API_PORT=8080
BRIDGE_API_HOST=localhost

# Logging Configuration
LOG_LEVEL=INFO                    # TRACE, DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FILE=logs/bridge.log

# Performance Monitoring
ENABLE_PERFORMANCE_LOGGING=true
ENABLE_AUDIT_LOGGING=true
ENABLE_STRUCTURED_LOGGING=true
```

### AI Service Configuration

```bash
# Anthropic Claude API
ANTHROPIC_API_KEY=your_anthropic_key_here
ANTHROPIC_MODEL=claude-sonnet-4-20250514

# OpenAI API (optional)
OPENAI_API_KEY=your_openai_key_here
OPENAI_MODEL=gpt-4

# SWE-Agent Configuration
SWE_AGENT_PORT=8000
SWE_AGENT_MODEL=claude-sonnet-4-20250514
SWE_AGENT_TIMEOUT=300
```

### Security Configuration

```bash
# OAuth Configuration
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
MICROSOFT_CLIENT_ID=your_microsoft_client_id
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret

# JWT Configuration
JWT_SECRET_KEY=your_jwt_secret_key
JWT_EXPIRATION_HOURS=24
JWT_REFRESH_DAYS=30

# Security Settings
ENABLE_RATE_LIMITING=true
RATE_LIMIT_PER_MINUTE=1000
MAX_FILE_SIZE_MB=10
```

### Development Configuration

```bash
# Development Mode
DEBUG=false
DEVELOPMENT_MODE=false
HOT_RELOAD=false

# Testing
TEST_MODE=false
MOCK_AI_RESPONSES=false
SKIP_AUTH=false
```

## 📄 Configuration Files

### Main Configuration (config.yaml)

Create `config.yaml` in the project root:

```yaml
# Bridge Configuration
bridge:
  api_host: localhost
  api_port: 8080
  vim_port: 8081
  max_concurrent_sessions: 10
  request_timeout: 30

# SWE-Agent Configuration
swe_agent:
  api_host: localhost
  api_port: 8000
  model: claude-sonnet-4-20250514
  timeout: 300
  max_retries: 3
  tools:
    - str_replace_editor
    - bash
    - find_file
    - search_file

# Logging Configuration
logging:
  level: INFO
  file: logs/bridge.log
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  max_size: 10MB
  backup_count: 5
  
  # Component-specific settings
  components:
    api:
      level: INFO
      file: logs/api.log
    chat:
      level: DEBUG
      file: logs/chat.log
    swe_agent:
      level: INFO
      file: logs/swe_agent.log
    performance:
      level: INFO
      file: logs/performance.log

# Security Configuration
security:
  rate_limit_per_minute: 1000
  max_file_size_mb: 10
  allowed_file_extensions:
    - .py
    - .js
    - .ts
    - .java
    - .cpp
    - .c
    - .h
    - .hpp
    - .md
    - .txt
    - .json
    - .yaml
    - .yml
  
  # OAuth Settings
  oauth:
    github:
      client_id: ${GITHUB_CLIENT_ID}
      client_secret: ${GITHUB_CLIENT_SECRET}
      scope: "read:user user:email"
    microsoft:
      client_id: ${MICROSOFT_CLIENT_ID}
      client_secret: ${MICROSOFT_CLIENT_SECRET}
      scope: "openid profile email"

# Performance Configuration
performance:
  enable_monitoring: true
  metrics_retention_minutes: 60
  background_monitoring_interval: 30
  request_tracking: true
  memory_monitoring: true
  cpu_monitoring: true

# AI Model Configuration
ai_models:
  default_provider: anthropic
  
  anthropic:
    api_key: ${ANTHROPIC_API_KEY}
    model: claude-sonnet-4-20250514
    max_tokens: 4096
    temperature: 0.1
    timeout: 30
  
  openai:
    api_key: ${OPENAI_API_KEY}
    model: gpt-4
    max_tokens: 4096
    temperature: 0.1
    timeout: 30

# Editor Integration
editors:
  vscode:
    extension_id: ai-coding-agent
    auto_completion: true
    chat_panel: true
    status_bar: true
  
  vim:
    plugin_enabled: true
    completion_trigger: "<C-Space>"
    chat_command: ":AugmentChat"
```

### Development Configuration (config.dev.yaml)

For development environments:

```yaml
# Development overrides
bridge:
  api_port: 8080

logging:
  level: DEBUG
  
  components:
    api:
      level: DEBUG
    chat:
      level: DEBUG
    swe_agent:
      level: DEBUG

security:
  rate_limit_per_minute: 10000  # Higher limit for development

performance:
  background_monitoring_interval: 10  # More frequent monitoring

ai_models:
  anthropic:
    temperature: 0.2  # More creative responses for testing
```

### Production Configuration (config.prod.yaml)

For production environments:

```yaml
# Production overrides
bridge:
  api_host: 0.0.0.0  # Listen on all interfaces

logging:
  level: WARNING
  
  components:
    api:
      level: INFO
    chat:
      level: INFO
    swe_agent:
      level: WARNING

security:
  rate_limit_per_minute: 500  # Stricter limits

performance:
  background_monitoring_interval: 60  # Less frequent monitoring

ai_models:
  anthropic:
    temperature: 0.05  # More deterministic responses
```

## 🎛️ Advanced Configuration

### Custom Logging Configuration

```yaml
logging:
  # Custom formatters
  formatters:
    detailed:
      format: "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"
    json:
      class: bridge.core.enhanced_logging.StructuredFormatter
  
  # Custom handlers
  handlers:
    file:
      class: logging.handlers.RotatingFileHandler
      filename: logs/bridge.log
      maxBytes: 10485760  # 10MB
      backupCount: 5
      formatter: detailed
    
    syslog:
      class: logging.handlers.SysLogHandler
      address: /dev/log
      formatter: json
  
  # Logger configuration
  loggers:
    bridge.api:
      level: INFO
      handlers: [file, syslog]
      propagate: false
```

### Performance Tuning

```yaml
performance:
  # Request handling
  max_concurrent_requests: 100
  request_queue_size: 1000
  worker_threads: 4
  
  # Memory management
  memory_limit_mb: 2048
  gc_threshold: 1000
  
  # Caching
  enable_response_cache: true
  cache_ttl_seconds: 300
  max_cache_size: 1000
  
  # Connection pooling
  connection_pool_size: 20
  connection_timeout: 30
```

### Security Hardening

```yaml
security:
  # Authentication
  require_auth: true
  session_timeout_minutes: 60
  max_login_attempts: 5
  lockout_duration_minutes: 15
  
  # Input validation
  max_request_size_mb: 50
  validate_file_types: true
  sanitize_inputs: true
  
  # CORS settings
  cors:
    origins: ["http://localhost:3000", "https://yourdomain.com"]
    methods: ["GET", "POST", "PUT", "DELETE"]
    headers: ["Content-Type", "Authorization"]
  
  # SSL/TLS
  ssl:
    enabled: false
    cert_file: /path/to/cert.pem
    key_file: /path/to/key.pem
```

## 🔄 Configuration Loading

### Loading Order

The system loads configuration in this order:

1. **Default configuration** (built-in)
2. **config.yaml** (if exists)
3. **Environment-specific config** (config.dev.yaml, config.prod.yaml)
4. **Environment variables** (override file settings)
5. **Command line arguments** (override everything)

### Environment-Specific Loading

```bash
# Development
ENVIRONMENT=development python start_api_server.py

# Production
ENVIRONMENT=production python start_api_server.py

# Custom config file
CONFIG_FILE=config.custom.yaml python start_api_server.py
```

### Runtime Configuration Changes

Some settings can be changed at runtime:

```bash
# Change log level
curl -X POST http://localhost:8080/api/config/logging \
  -H "Content-Type: application/json" \
  -d '{"level": "DEBUG"}'

# Update rate limits
curl -X POST http://localhost:8080/api/config/security \
  -H "Content-Type: application/json" \
  -d '{"rate_limit_per_minute": 2000}'
```

## 🧪 Configuration Validation

### Validate Configuration

```bash
# Validate configuration file
python -c "
from bridge.core.config import config
print('Configuration valid:', config.validate())
print('Loaded from:', config.config_file)
"

# Check specific settings
python -c "
from bridge.core.config import config
print('API Port:', config.get('bridge', 'api_port'))
print('Log Level:', config.get('logging', 'level'))
print('AI Model:', config.get('ai_models', 'anthropic', 'model'))
"
```

### Configuration Schema

The system validates configuration against a schema:

```python
# Example validation
from bridge.core.config import ConfigValidator

validator = ConfigValidator()
errors = validator.validate_file('config.yaml')
if errors:
    print('Configuration errors:', errors)
else:
    print('Configuration is valid')
```

## 📚 Configuration Examples

### Minimal Configuration

```yaml
# Minimal config.yaml
bridge:
  api_port: 8080

ai_models:
  anthropic:
    api_key: ${ANTHROPIC_API_KEY}
```

### Complete Development Setup

```yaml
# Complete development configuration
bridge:
  api_port: 8080
  max_concurrent_sessions: 5

logging:
  level: DEBUG
  components:
    api:
      level: DEBUG

security:
  rate_limit_per_minute: 10000
  require_auth: false

ai_models:
  anthropic:
    api_key: ${ANTHROPIC_API_KEY}
    temperature: 0.2

performance:
  enable_monitoring: true
  background_monitoring_interval: 10
```

### Production Setup

```yaml
# Production configuration
bridge:
  api_host: 0.0.0.0
  api_port: 8080
  max_concurrent_sessions: 50

logging:
  level: INFO
  max_size: 50MB
  backup_count: 10

security:
  rate_limit_per_minute: 1000
  require_auth: true
  cors:
    origins: ["https://yourdomain.com"]

ai_models:
  anthropic:
    api_key: ${ANTHROPIC_API_KEY}
    temperature: 0.05

performance:
  enable_monitoring: true
  memory_limit_mb: 4096
```

---

*For more configuration options, see the [Configuration Reference](../reference/CONFIGURATION.md)*
