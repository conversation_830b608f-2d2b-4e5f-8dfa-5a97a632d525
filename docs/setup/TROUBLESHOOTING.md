# Troubleshooting Guide

This guide helps you resolve common issues with the AI Coding Agent Bridge system.

## 🚨 Common Issues

### Installation Problems

#### Python Version Issues
**Problem**: `SWE-Agent requires Python 3.11 or higher`

**Solution**:
```bash
# Check Python version
python --version

# Install Python 3.11+ if needed
# On Ubuntu/Debian:
sudo apt update
sudo apt install python3.11 python3.11-venv

# On macOS with Homebrew:
brew install python@3.11

# Create virtual environment with specific Python version
python3.11 -m venv venv
source venv/bin/activate
```

#### Missing Dependencies
**Problem**: `ModuleNotFoundError` or import errors

**Solution**:
```bash
# Reinstall all dependencies
pip install --force-reinstall -r requirements.txt

# Install specific missing packages
pip install psutil flask flask-cors flask-socketio

# For SWE-Agent integration
pip install -e ./swe-agent
```

#### Permission Issues
**Problem**: Permission denied errors during installation

**Solution**:
```bash
# Fix permissions on Linux/macOS
chmod +x start_api_server.py
chmod +x activate_venv.sh

# Use virtual environment to avoid system-wide installation
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### Server Startup Issues

#### Port Already in Use
**Problem**: `Address already in use` or `Port 8080 is already in use`

**Solution**:
```bash
# Find process using the port
lsof -i :8080
netstat -tulpn | grep :8080

# Kill the process
kill -9 <PID>

# Or use a different port
BRIDGE_API_PORT=8081 python start_api_server.py
```

#### Environment Variables Not Loaded
**Problem**: Configuration not loading or API keys missing

**Solution**:
```bash
# Check if .env file exists
ls -la .env

# Create .env file if missing
cp .env.example .env

# Edit .env file with your configuration
nano .env

# Verify environment variables are loaded
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
print('API Port:', os.getenv('BRIDGE_API_PORT'))
print('Log Level:', os.getenv('LOG_LEVEL'))
"
```

#### Memory Issues
**Problem**: Out of memory errors or high memory usage

**Solution**:
```bash
# Check system memory
free -h

# Monitor memory usage
top -p $(pgrep -f "python start_api_server.py")

# Reduce memory usage by limiting concurrent sessions
# Edit config.yaml:
# max_concurrent_sessions: 5
# memory_limit_mb: 1024
```

### API and Communication Issues

#### API Server Not Responding
**Problem**: Connection refused or timeout errors

**Solution**:
```bash
# Check if server is running
curl http://localhost:8080/health

# Check server logs
tail -f logs/bridge.log

# Restart server with debug logging
LOG_LEVEL=DEBUG python start_api_server.py

# Check firewall settings
sudo ufw status
sudo iptables -L
```

#### WebSocket Connection Issues
**Problem**: WebSocket connections failing or dropping

**Solution**:
```bash
# Test WebSocket connection
wscat -c ws://localhost:8080/socket.io/

# Check proxy settings
echo $HTTP_PROXY
echo $HTTPS_PROXY

# Disable proxy for localhost
export NO_PROXY=localhost,127.0.0.1
```

#### Authentication Failures
**Problem**: OAuth or JWT token issues

**Solution**:
```bash
# Check OAuth configuration
curl -X POST http://localhost:8080/auth/device \
  -d "client_id=ai-coding-agent-default&scope=read write"

# Verify JWT token
python -c "
import jwt
token = 'your_jwt_token_here'
try:
    decoded = jwt.decode(token, options={'verify_signature': False})
    print('Token valid:', decoded)
except Exception as e:
    print('Token error:', e)
"

# Clear authentication cache
rm -rf ~/.ai-coding-agent/auth/
```

### Editor Integration Issues

#### VS Code Extension Not Working
**Problem**: Extension not loading or commands not available

**Solution**:
1. **Check Extension Installation**:
   ```bash
   code --list-extensions | grep ai-coding-agent
   ```

2. **Verify API Connection**:
   - Open VS Code Developer Console (Help → Toggle Developer Tools)
   - Check for connection errors
   - Verify API URL in settings

3. **Reinstall Extension**:
   ```bash
   code --uninstall-extension ai-coding-agent
   code --install-extension vscode-extension/ai-coding-agent-0.2.0.vsix
   ```

#### Vim Plugin Issues
**Problem**: Vim commands not working or plugin not loading

**Solution**:
1. **Check Plugin Installation**:
   ```vim
   :echo &runtimepath
   ```

2. **Verify Configuration**:
   ```vim
   :echo g:augment_enabled
   :echo g:augment_api_url
   ```

3. **Test Connection**:
   ```vim
   :AugmentPing
   ```

### Performance Issues

#### Slow Response Times
**Problem**: API requests taking too long

**Solution**:
```bash
# Check performance metrics
curl http://localhost:8080/api/performance

# Monitor system resources
htop
iostat -x 1

# Check network latency
ping api.anthropic.com
ping api.openai.com

# Optimize configuration
# Edit config.yaml:
# request_timeout: 30
# max_workers: 4
```

#### High CPU Usage
**Problem**: Server consuming too much CPU

**Solution**:
```bash
# Profile CPU usage
python -m cProfile start_api_server.py

# Check for infinite loops in logs
grep -i "loop\|infinite\|stuck" logs/bridge.log

# Limit concurrent requests
# Edit config.yaml:
# max_concurrent_requests: 10
# rate_limit_per_minute: 100
```

### Logging and Debugging

#### Logs Not Appearing
**Problem**: Log files empty or not being created

**Solution**:
```bash
# Check log directory permissions
ls -la logs/
chmod 755 logs/
chmod 644 logs/*.log

# Verify logging configuration
python -c "
from bridge.core.enhanced_logging import logging_manager
print('Log directory:', logging_manager.logs_dir)
print('Handlers:', list(logging_manager.handlers.keys()))
"

# Test logging manually
python -c "
from bridge.core.enhanced_logging import get_api_logger
logger = get_api_logger('test')
logger.info('Test log message')
"
```

#### Debug Mode Not Working
**Problem**: Debug logs not showing

**Solution**:
```bash
# Set debug level explicitly
export LOG_LEVEL=DEBUG
python start_api_server.py

# Check log level in code
python -c "
import logging
from bridge.core.config import config
print('Configured log level:', config.get('logging', 'level'))
print('Python log level:', logging.getLogger().level)
"
```

## 🔧 Diagnostic Commands

### System Health Check
```bash
#!/bin/bash
echo "=== AI Coding Agent Bridge Health Check ==="

echo "1. Python Version:"
python --version

echo "2. Virtual Environment:"
which python
echo $VIRTUAL_ENV

echo "3. Dependencies:"
pip list | grep -E "(flask|anthropic|openai|psutil)"

echo "4. Server Status:"
curl -s http://localhost:8080/health | jq . || echo "Server not responding"

echo "5. Log Files:"
ls -la logs/

echo "6. System Resources:"
free -h
df -h

echo "7. Network Connectivity:"
ping -c 1 api.anthropic.com
ping -c 1 api.openai.com

echo "=== Health Check Complete ==="
```

### Performance Diagnostics
```bash
#!/bin/bash
echo "=== Performance Diagnostics ==="

echo "1. API Performance:"
curl -s http://localhost:8080/api/performance | jq .

echo "2. System Metrics:"
top -bn1 | head -20

echo "3. Memory Usage:"
ps aux | grep python | grep -v grep

echo "4. Disk I/O:"
iostat -x 1 1

echo "5. Network Connections:"
netstat -tulpn | grep :8080

echo "=== Diagnostics Complete ==="
```

## 📞 Getting Help

### Before Reporting Issues

1. **Check the logs**:
   ```bash
   tail -f logs/bridge.log
   tail -f logs/errors.log
   ```

2. **Run health check**:
   ```bash
   curl http://localhost:8080/health
   ```

3. **Verify configuration**:
   ```bash
   cat .env
   python -c "from bridge.core.config import config; print(config.config)"
   ```

### Reporting Issues

When reporting issues, please include:

1. **System Information**:
   - Operating system and version
   - Python version
   - Virtual environment details

2. **Error Details**:
   - Complete error message
   - Stack trace if available
   - Steps to reproduce

3. **Configuration**:
   - Relevant configuration settings (sanitize API keys)
   - Environment variables
   - Log excerpts

4. **Context**:
   - What you were trying to do
   - Expected vs actual behavior
   - Recent changes to the system

### Support Channels

- **GitHub Issues**: [Report bugs and feature requests](https://github.com/your-org/AI-Coding-Agent/issues)
- **Discussions**: [Community support and questions](https://github.com/your-org/AI-Coding-Agent/discussions)
- **Documentation**: [Complete documentation](../README.md)

## 🔄 Recovery Procedures

### Complete Reset
If all else fails, perform a complete reset:

```bash
# 1. Stop all services
pkill -f "python start_api_server.py"

# 2. Clean up
rm -rf venv/
rm -rf logs/
rm -rf __pycache__/
find . -name "*.pyc" -delete

# 3. Reinstall
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 4. Reconfigure
cp .env.example .env
# Edit .env with your settings

# 5. Restart
python start_api_server.py
```

---

*If you continue to experience issues, please check the [FAQ](../reference/FAQ.md) or reach out to the community for support.*
