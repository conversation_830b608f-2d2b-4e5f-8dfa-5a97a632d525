# Quick Start Guide

Get the AI Coding Agent Bridge up and running in under 10 minutes!

## ⚡ 5-Minute Setup

### 1. Prerequisites Check
```bash
# Verify Python version (3.8+ required)
python --version

# Verify Git is installed
git --version
```

### 2. <PERSON><PERSON> and Setup
```bash
# Clone the repository
git clone https://github.com/your-org/AI-Coding-Agent.git
cd AI-Coding-Agent

# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
pip install psutil  # For performance monitoring
```

### 3. Basic Configuration
```bash
# Create environment file
cat > .env << EOF
BRIDGE_API_PORT=8080
LOG_LEVEL=INFO
ANTHROPIC_API_KEY=your_key_here
EOF
```

### 4. Start the Server
```bash
python start_api_server.py
```

🎉 **Success!** Your server is now running at `http://localhost:8080`

## 🧪 Quick Test

### Test the API
```bash
# Health check
curl http://localhost:8080/health

# Performance metrics
curl http://localhost:8080/api/performance
```

Expected health check response:
```json
{
  "status": "ok",
  "timestamp": **********.376,
  "system": {
    "memory": {"percent": 68.5},
    "cpu": {"percent": 0.0},
    "active_requests": 0
  }
}
```

## 🔌 Quick Integration

### VS Code Extension
1. Open VS Code
2. Install the extension:
   ```bash
   code --install-extension vscode-extension/ai-coding-agent-0.2.0.vsix
   ```
3. Configure the API endpoint in VS Code settings:
   ```json
   {
     "aiCodingAgent.apiUrl": "http://localhost:8080"
   }
   ```

### Test Code Completion
1. Open a Python file in VS Code
2. Start typing code
3. Use `Ctrl+Space` to trigger AI completion
4. Use `Ctrl+Shift+P` → "AI Coding Agent: Start Chat" for chat interface

## 🚀 Common Use Cases

### 1. Code Completion
```python
# Type this in VS Code and trigger completion
def fibonacci(n):
    # AI will suggest the implementation
```

### 2. Chat Interface
```bash
# Start a chat session via API
curl -X POST http://localhost:8080/api/chat/sessions \
  -H "Content-Type: application/json" \
  -d '{"title": "My Coding Session"}'
```

### 3. File Operations
```bash
# List workspace files
curl http://localhost:8080/api/workspace/files

# Read a file
curl http://localhost:8080/api/files/read \
  -H "Content-Type: application/json" \
  -d '{"file_path": "example.py"}'
```

### 4. SWE-Agent Tools
```bash
# Execute a search tool
curl -X POST http://localhost:8080/api/swe-tools/execute \
  -H "Content-Type: application/json" \
  -d '{
    "tool": "str_replace_editor",
    "parameters": {
      "command": "view",
      "path": "example.py"
    }
  }'
```

## 📊 Monitoring

### View Logs
```bash
# Real-time logs
tail -f logs/bridge.log

# Performance logs
tail -f logs/performance.log

# Error logs
tail -f logs/errors.log
```

### Performance Dashboard
Visit `http://localhost:8080/api/performance` for real-time metrics:
- System resource usage
- Request performance
- Endpoint statistics

## 🔧 Configuration

### Essential Settings

Edit `.env` file:
```bash
# API Configuration
BRIDGE_API_PORT=8080
BRIDGE_API_HOST=localhost

# Logging
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR

# AI Models (choose one)
ANTHROPIC_API_KEY=your_anthropic_key
OPENAI_API_KEY=your_openai_key

# SWE-Agent Model
SWE_AGENT_MODEL=claude-sonnet-4-20250514
```

### Advanced Configuration

Create `config.yaml`:
```yaml
bridge:
  api_port: 8080
  
logging:
  level: INFO
  
security:
  rate_limit_per_minute: 1000
  max_file_size_mb: 10
```

## 🎯 Next Steps

### For Developers
1. **Explore APIs**: Check [REST API Documentation](../api/REST_API.md)
2. **Set up Development**: Follow [Development Setup](../development/SETUP.md)
3. **Contribute**: Read [Contributing Guidelines](../development/CONTRIBUTING.md)

### For Users
1. **VS Code Setup**: Complete [VS Code Integration](../integrations/VSCODE.md)
2. **Vim Setup**: Configure [Vim Integration](../integrations/VIM.md)
3. **Chat Features**: Learn about [Chat System](../integrations/CHAT.md)

### For Administrators
1. **Production Setup**: Review [Deployment Guide](../operations/DEPLOYMENT.md)
2. **Monitoring**: Configure [Logging & Monitoring](../operations/LOGGING.md)
3. **Security**: Set up [Authentication](../api/AUTHENTICATION.md)

## 🆘 Need Help?

### Quick Fixes

**Server won't start?**
```bash
# Check if port is in use
lsof -i :8080

# Try different port
BRIDGE_API_PORT=8081 python start_api_server.py
```

**VS Code extension not working?**
1. Check VS Code settings for correct API URL
2. Verify server is running: `curl http://localhost:8080/health`
3. Check VS Code Developer Console for errors

**Performance issues?**
```bash
# Check system resources
curl http://localhost:8080/api/performance

# View performance logs
tail -f logs/performance.log
```

### Get Support
- 📖 [Full Documentation](../README.md)
- 🐛 [Troubleshooting Guide](TROUBLESHOOTING.md)
- 💬 [GitHub Discussions](https://github.com/your-org/AI-Coding-Agent/discussions)
- 🚨 [Report Issues](https://github.com/your-org/AI-Coding-Agent/issues)

---

**🎉 You're all set!** The AI Coding Agent Bridge is now ready to enhance your coding experience.

*Next: [System Architecture](../architecture/SYSTEM_ARCHITECTURE.md) to understand how it all works.*
