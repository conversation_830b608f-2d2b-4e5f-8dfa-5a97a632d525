# REST API Documentation

The AI Coding Agent Bridge provides a comprehensive REST API for all core functionality. All endpoints return JSON responses and support standard HTTP status codes.

## 🌐 Base URL

```
http://localhost:8080
```

## 📋 API Overview

### Core Endpoints
- **Health & Monitoring**: System status and performance metrics
- **Authentication**: OAuth and session management
- **Chat**: Multi-turn conversation interface
- **Completion**: AI-powered code completion
- **File Operations**: Workspace file management
- **SWE-Agent Tools**: Advanced development tools
- **Sessions**: Multi-session management

## 🏥 Health & Monitoring

### GET /health
Get system health status and basic metrics.

**Response:**
```json
{
  "status": "ok",
  "timestamp": **********.376,
  "system": {
    "memory": {
      "percent": 68.5,
      "available_mb": 4935.06,
      "used_mb": 9064.37,
      "total_mb": 15669.48
    },
    "cpu": {
      "percent": 0.0
    },
    "active_requests": 0,
    "total_metrics": 2
  }
}
```

### GET /api/performance
Get detailed performance metrics and statistics.

**Query Parameters:**
- `minutes` (optional): Time window for metrics (default: 5)

**Response:**
```json
{
  "system": {
    "memory": {"percent": 68.5, "available_mb": 4935.06},
    "cpu": {"percent": 0.0},
    "active_requests": 0,
    "total_metrics": 3
  },
  "recent_metrics": [
    {
      "name": "memory_usage",
      "timestamp": "2025-06-06T11:43:40.406868",
      "type": "system",
      "unit": "percent",
      "value": 69.4
    }
  ],
  "endpoint_stats": {
    "GET /health": {
      "count": 5,
      "avg_duration": 0.002,
      "min_duration": 0.001,
      "max_duration": 0.005,
      "p95_duration": 0.004
    }
  }
}
```

## 💬 Chat API

### POST /api/chat/sessions
Create a new chat session.

**Request Body:**
```json
{
  "title": "My Coding Session",
  "context": {
    "file_path": "/path/to/file.py",
    "project_root": "/path/to/project",
    "language": "python"
  }
}
```

**Response:**
```json
{
  "status": "success",
  "session_id": "sess_abc123",
  "message": "Chat session created successfully"
}
```

### GET /api/chat/sessions
List all chat sessions.

**Response:**
```json
{
  "status": "success",
  "sessions": [
    {
      "id": "sess_abc123",
      "title": "My Coding Session",
      "created_at": "2025-06-06T11:43:40Z",
      "status": "active",
      "message_count": 5
    }
  ]
}
```

### POST /api/chat/sessions/{session_id}/messages
Send a message to a chat session.

**Request Body:**
```json
{
  "message": "How do I implement a binary search?",
  "context": {
    "file_path": "/path/to/file.py",
    "content": "def search_array(arr, target):",
    "cursor_line": 1,
    "cursor_column": 30,
    "language": "python"
  }
}
```

**Response:**
```json
{
  "status": "success",
  "response": "Here's how to implement binary search...",
  "session_id": "sess_abc123"
}
```

### POST /api/chat/sessions/{session_id}/stream
Send a message and receive streaming response.

**Request Body:** Same as above

**Response:** Server-Sent Events (SSE)
```
data: {"chunk": "Here's how"}
data: {"chunk": " to implement"}
data: {"chunk": " binary search..."}
data: {"done": true}
```

## 🔧 Code Completion

### POST /api/completion
Get AI-powered code completion suggestions.

**Request Body:**
```json
{
  "file_path": "/path/to/file.py",
  "content": "def fibonacci(n):\n    if n <= 1:\n        return n\n    ",
  "cursor_position": {"line": 3, "column": 4},
  "language": "python",
  "max_suggestions": 3
}
```

**Response:**
```json
{
  "status": "success",
  "suggestions": [
    {
      "text": "return fibonacci(n-1) + fibonacci(n-2)",
      "confidence": 0.95,
      "type": "completion"
    }
  ],
  "context_used": true
}
```

### POST /api/completion/analyze
Analyze code with SWE-Agent tools for enhanced completion.

**Request Body:**
```json
{
  "file_path": "/path/to/file.py",
  "content": "def process_data(data):",
  "cursor_position": {"line": 1, "column": 23},
  "tools": ["filemap", "search"]
}
```

**Response:**
```json
{
  "status": "success",
  "analysis": {
    "file_structure": {...},
    "search_results": [...],
    "suggestions": [...]
  },
  "tools_used": ["filemap", "search"]
}
```

## 📁 File Operations

### GET /api/workspace/files
List files in the workspace.

**Query Parameters:**
- `path` (optional): Directory path to list
- `recursive` (optional): Include subdirectories (default: false)

**Response:**
```json
{
  "status": "success",
  "files": [
    {
      "name": "main.py",
      "path": "/workspace/main.py",
      "type": "file",
      "size": 1024,
      "modified": "2025-06-06T11:43:40Z"
    }
  ]
}
```

### POST /api/files/read
Read file content.

**Request Body:**
```json
{
  "file_path": "/path/to/file.py",
  "encoding": "utf-8"
}
```

**Response:**
```json
{
  "status": "success",
  "content": "def hello_world():\n    print('Hello, World!')",
  "encoding": "utf-8",
  "size": 45
}
```

### POST /api/files/write
Write content to a file.

**Request Body:**
```json
{
  "file_path": "/path/to/file.py",
  "content": "def hello_world():\n    print('Hello, World!')",
  "encoding": "utf-8",
  "create_dirs": true
}
```

**Response:**
```json
{
  "status": "success",
  "message": "File written successfully",
  "bytes_written": 45
}
```

## 🛠️ SWE-Agent Tools

### GET /api/swe-tools/list
List available SWE-Agent tools.

**Response:**
```json
{
  "status": "success",
  "tools": [
    {
      "name": "str_replace_editor",
      "description": "Edit files using string replacement",
      "parameters": ["command", "path", "file_text", "old_str", "new_str"]
    },
    {
      "name": "bash",
      "description": "Execute bash commands",
      "parameters": ["command"]
    }
  ]
}
```

### POST /api/swe-tools/execute
Execute a SWE-Agent tool.

**Request Body:**
```json
{
  "tool": "str_replace_editor",
  "parameters": {
    "command": "view",
    "path": "/path/to/file.py"
  },
  "working_directory": "/workspace",
  "timeout": 30
}
```

**Response:**
```json
{
  "status": "success",
  "result": {
    "success": true,
    "output": "File content here...",
    "error": null,
    "execution_time": 0.123
  }
}
```

## 🔐 Authentication

### POST /api/auth/oauth/github
Initiate GitHub OAuth flow.

**Response:**
```json
{
  "status": "success",
  "auth_url": "https://github.com/login/oauth/authorize?client_id=...",
  "state": "random_state_string"
}
```

### GET /api/auth/oauth/github/callback
Handle OAuth callback (typically called by OAuth provider).

**Query Parameters:**
- `code`: Authorization code from OAuth provider
- `state`: State parameter for CSRF protection

### GET /api/auth/user
Get current user information.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "status": "success",
  "user": {
    "id": "user_123",
    "username": "developer",
    "email": "<EMAIL>",
    "provider": "github"
  }
}
```

## 📊 Session Management

### POST /api/sessions
Create a new agent session.

**Request Body:**
```json
{
  "problem_statement": "Fix the bug in the authentication system",
  "repo_path": "/path/to/repository",
  "model_name": "claude-sonnet-4-20250514",
  "tools": ["str_replace_editor", "bash"],
  "environment": {
    "python_version": "3.11"
  }
}
```

**Response:**
```json
{
  "status": "created",
  "session_id": "sess_xyz789"
}
```

### GET /api/sessions
List all sessions.

**Query Parameters:**
- `status` (optional): Filter by status (created, running, completed, failed)

**Response:**
```json
{
  "sessions": [
    {
      "id": "sess_xyz789",
      "status": "running",
      "created_at": "2025-06-06T11:43:40Z",
      "problem_statement": "Fix the bug...",
      "progress": 0.3
    }
  ]
}
```

## 📝 Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error description",
  "code": "ERROR_CODE",
  "details": {
    "field": "Additional error details"
  }
}
```

### Common HTTP Status Codes
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `429`: Too Many Requests
- `500`: Internal Server Error

## 🔄 Rate Limiting

API requests are rate-limited to prevent abuse:

- **Default Limit**: 1000 requests per minute per IP
- **Headers**: Rate limit information in response headers
  ```
  X-RateLimit-Limit: 1000
  X-RateLimit-Remaining: 999
  X-RateLimit-Reset: 1749190500
  ```

## 📡 WebSocket API

For real-time features, see [WebSocket API Documentation](WEBSOCKET_API.md).

---

*For authentication details, see [Authentication Guide](AUTHENTICATION.md)*
*For error handling, see [Error Handling Guide](ERROR_HANDLING.md)*
