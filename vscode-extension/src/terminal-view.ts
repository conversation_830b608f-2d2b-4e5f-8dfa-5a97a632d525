import * as vscode from 'vscode';
import { BridgeClient } from './bridge-client';

/**
 * Terminal View for showing AI agent execution output
 */
export class TerminalView {
    private panel: vscode.WebviewPanel | undefined;
    private bridgeClient: BridgeClient;
    private context: vscode.ExtensionContext;
    private terminalOutput: string[] = [];
    private currentSessionId: string | null = null;

    constructor(context: vscode.ExtensionContext, bridgeClient: BridgeClient) {
        this.context = context;
        this.bridgeClient = bridgeClient;
    }

    /**
     * Show the terminal view
     */
    public show() {
        if (this.panel) {
            this.panel.reveal(vscode.ViewColumn.Two);
            return;
        }

        this.panel = vscode.window.createWebviewPanel(
            'aiCodingAgentTerminal',
            'AI Agent Terminal',
            vscode.ViewColumn.Two,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        this.updateWebview();

        this.panel.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.command) {
                    case 'clearTerminal':
                        this.clearTerminal();
                        break;
                    case 'stopExecution':
                        await this.stopExecution();
                        break;
                }
            },
            undefined,
            this.context.subscriptions
        );

        this.panel.onDidDispose(
            () => {
                this.panel = undefined;
            },
            null,
            this.context.subscriptions
        );
    }

    /**
     * Add output to terminal
     */
    public addOutput(text: string, type: 'stdout' | 'stderr' | 'system' = 'stdout') {
        const timestamp = new Date().toLocaleTimeString();
        const formattedOutput = `[${timestamp}] ${text}`;
        this.terminalOutput.push(JSON.stringify({ type, content: formattedOutput, timestamp }));
        this.updateWebview();
        this.scrollToBottom();
    }

    /**
     * Set current session ID
     */
    public setSessionId(sessionId: string) {
        this.currentSessionId = sessionId;
        this.addOutput(`Session started: ${sessionId}`, 'system');
    }

    /**
     * Clear terminal output
     */
    private clearTerminal() {
        this.terminalOutput = [];
        this.updateWebview();
    }

    /**
     * Stop current execution
     */
    private async stopExecution() {
        try {
            await this.bridgeClient.stopAgent();
            this.addOutput('Execution stopped by user', 'system');
        } catch (error: any) {
            this.addOutput(`Error stopping execution: ${error.message}`, 'stderr');
        }
    }

    /**
     * Scroll to bottom
     */
    private scrollToBottom() {
        if (this.panel) {
            this.panel.webview.postMessage({ command: 'scrollToBottom' });
        }
    }

    /**
     * Update webview content
     */
    private updateWebview() {
        if (!this.panel) {
            return;
        }

        this.panel.webview.html = this.getHtmlForWebview();
    }

    /**
     * Get HTML for webview
     */
    private getHtmlForWebview(): string {
        const outputHtml = this.terminalOutput.map(outputJson => {
            const output = JSON.parse(outputJson);
            const typeClass = output.type;
            return `
                <div class="terminal-line ${typeClass}">
                    <span class="line-content">${this.escapeHtml(output.content)}</span>
                </div>
            `;
        }).join('');

        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>AI Agent Terminal</title>
                <style>
                    body {
                        font-family: 'Courier New', monospace;
                        font-size: 13px;
                        color: var(--vscode-terminal-foreground);
                        background-color: var(--vscode-terminal-background);
                        padding: 0;
                        margin: 0;
                        display: flex;
                        flex-direction: column;
                        height: 100vh;
                    }
                    
                    .terminal-header {
                        padding: 8px 12px;
                        background-color: var(--vscode-editorWidget-background);
                        border-bottom: 1px solid var(--vscode-panel-border);
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                    
                    .terminal-title {
                        font-weight: bold;
                        color: var(--vscode-foreground);
                    }
                    
                    .terminal-controls {
                        display: flex;
                        gap: 8px;
                    }
                    
                    .control-btn {
                        background-color: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border: none;
                        padding: 4px 8px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 11px;
                    }
                    
                    .control-btn:hover {
                        background-color: var(--vscode-button-hoverBackground);
                    }
                    
                    .terminal-output {
                        flex: 1;
                        overflow-y: auto;
                        padding: 8px;
                        white-space: pre-wrap;
                        word-wrap: break-word;
                    }
                    
                    .terminal-line {
                        margin-bottom: 2px;
                        line-height: 1.4;
                    }
                    
                    .terminal-line.stdout {
                        color: var(--vscode-terminal-foreground);
                    }
                    
                    .terminal-line.stderr {
                        color: var(--vscode-errorForeground);
                    }
                    
                    .terminal-line.system {
                        color: var(--vscode-terminal-ansiBlue);
                        font-style: italic;
                    }
                    
                    .line-content {
                        font-family: inherit;
                    }
                    
                    .status-bar {
                        padding: 4px 12px;
                        background-color: var(--vscode-statusBar-background);
                        color: var(--vscode-statusBar-foreground);
                        border-top: 1px solid var(--vscode-panel-border);
                        font-size: 11px;
                        display: flex;
                        justify-content: space-between;
                    }
                </style>
            </head>
            <body>
                <div class="terminal-header">
                    <div class="terminal-title">🖥️ AI Agent Terminal</div>
                    <div class="terminal-controls">
                        <button class="control-btn" onclick="clearTerminal()">🗑️ Clear</button>
                        <button class="control-btn" onclick="stopExecution()">⏹️ Stop</button>
                    </div>
                </div>
                
                <div class="terminal-output" id="terminal-output">
                    ${outputHtml || '<div class="terminal-line system"><span class="line-content">Terminal ready. Waiting for AI agent output...</span></div>'}
                </div>
                
                <div class="status-bar">
                    <span>Session: ${this.currentSessionId || 'None'}</span>
                    <span>Lines: ${this.terminalOutput.length}</span>
                </div>
                
                <script>
                    const vscode = acquireVsCodeApi();
                    
                    function clearTerminal() {
                        vscode.postMessage({ command: 'clearTerminal' });
                    }
                    
                    function stopExecution() {
                        vscode.postMessage({ command: 'stopExecution' });
                    }
                    
                    // Auto-scroll to bottom
                    function scrollToBottom() {
                        const output = document.getElementById('terminal-output');
                        output.scrollTop = output.scrollHeight;
                    }
                    
                    // Listen for scroll commands
                    window.addEventListener('message', event => {
                        const message = event.data;
                        if (message.command === 'scrollToBottom') {
                            scrollToBottom();
                        }
                    });
                    
                    // Initial scroll
                    scrollToBottom();
                </script>
            </body>
            </html>
        `;
    }

    /**
     * Escape HTML
     */
    private escapeHtml(text: string): string {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }
}
