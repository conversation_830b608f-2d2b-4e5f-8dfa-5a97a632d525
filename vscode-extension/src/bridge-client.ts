import * as vscode from 'vscode';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { io, Socket } from 'socket.io-client';
import { OAuthManager } from './auth/oauth-manager';

export interface ChatSession {
    session_id: string;
    title: string;
    status: string;
    created_at: string;
    updated_at: string;
}

/**
 * Response interface for API calls
 */
interface ApiResponse {
    status: string;
    error?: string;
    session_id?: string;
    message?: string;
    [key: string]: any;
}

/**
 * Session configuration interface
 */
interface SessionConfig {
    problem_statement: string;
    repo_path: string;
    model_name: string;
    tools?: string[] | Record<string, any>;
    environment?: Record<string, any>;
    continuous_mode?: boolean;
    enable_parallel?: boolean;
    execution_timeout?: number;
    total_execution_timeout?: number;
    max_consecutive_execution_timeouts?: number;
}

/**
 * Client for communicating with the AI Coding Agent bridge
 */
export class BridgeClient {
    private outputChannel: vscode.OutputChannel;
    private host: string;
    private port: number;
    private baseUrl: string;
    private statusCallback: (status: 'connected' | 'disconnected' | 'running' | 'error') => void;
    private httpClient: AxiosInstance;
    private socket: Socket | null = null;
    private currentSessionId: string | null = null;
    private oauthManager?: OAuthManager;
    private sessionOutputCallbacks: Map<string, (data: any) => void> = new Map();

    /**
     * Create a new BridgeClient
     *
     * @param outputChannel VS Code output channel for logging
     * @param statusCallback Callback function for status updates
     */
    constructor(
        outputChannel: vscode.OutputChannel,
        statusCallback: (status: 'connected' | 'disconnected' | 'running' | 'error') => void
    ) {
        const config = vscode.workspace.getConfiguration('aiCodingAgent');
        this.host = config.get<string>('bridgeHost') || 'localhost';
        this.port = config.get<number>('bridgePort') || 8080;
        this.baseUrl = `http://${this.host}:${this.port}`;
        this.outputChannel = outputChannel;
        this.statusCallback = statusCallback;

        // Initialize HTTP client with timeout and error handling
        this.httpClient = axios.create({
            baseURL: this.baseUrl,
            timeout: 1800000, // Increased to 30 minutes for continuous execution
            headers: {
                'Content-Type': 'application/json'
            },
            // Add retry configuration
            validateStatus: (status) => status < 500 // Don't throw on 4xx errors
        });

        // Add request interceptor for OAuth authentication
        this.httpClient.interceptors.request.use(
            (config) => {
                // Add OAuth token if available
                if (this.oauthManager) {
                    const token = this.oauthManager.getAccessToken();
                    if (token) {
                        if (!config.headers) {
                            config.headers = {};
                        }
                        config.headers.Authorization = `Bearer ${token}`;
                    }
                }
                return config;
            },
            (error) => Promise.reject(error)
        );

        // Add response interceptor for error handling and auth
        this.httpClient.interceptors.response.use(
            (response: AxiosResponse) => response,
            async (error) => {
                this.outputChannel.appendLine(`HTTP Error: ${error.message}`);
                if (error.response) {
                    this.outputChannel.appendLine(`Status: ${error.response.status}`);
                    this.outputChannel.appendLine(`Data: ${JSON.stringify(error.response.data)}`);

                    // Handle 401 Unauthorized - token might be expired
                    if (error.response.status === 401 && this.oauthManager) {
                        this.outputChannel.appendLine('Received 401 - attempting token refresh...');
                        const refreshed = await this.oauthManager.refreshAccessToken();
                        if (refreshed) {
                            // Retry the original request with new token
                            const originalRequest = error.config;
                            const token = this.oauthManager.getAccessToken();
                            if (token) {
                                originalRequest.headers.Authorization = `Bearer ${token}`;
                                return this.httpClient.request(originalRequest);
                            }
                        }
                    }
                }
                return Promise.reject(error);
            }
        );
    }

    /**
     * Set OAuth manager for authentication
     */
    public setOAuthManager(oauthManager: OAuthManager): void {
        this.oauthManager = oauthManager;
        this.outputChannel.appendLine('OAuth manager configured for bridge client');
    }

    /**
     * Connect to the bridge
     */
    public async connect(): Promise<void> {
        this.outputChannel.appendLine(`Connecting to AI Coding Agent bridge at ${this.baseUrl}...`);

        try {
            // Check if bridge is available
            await this.checkStatus();

            // Initialize WebSocket connection
            await this.initializeWebSocket();

            // Register workspace with bridge
            await this.registerWorkspace();

            this.outputChannel.appendLine('Successfully connected to AI Coding Agent bridge');
            this.statusCallback('connected');
        } catch (error: any) {
            this.outputChannel.appendLine(`Failed to connect to AI Coding Agent bridge: ${error.message}`);
            this.statusCallback('disconnected');
        }
    }

    /**
     * Register current workspace with the bridge
     */
    private async registerWorkspace(): Promise<void> {
        try {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (workspaceFolders && workspaceFolders.length > 0) {
                const workspacePath = workspaceFolders[0].uri.fsPath;
                this.outputChannel.appendLine(`Registering workspace: ${workspacePath}`);

                const response = await this.httpClient.post('/api/workspace/register', {
                    workspace_path: workspacePath,
                    client_id: 'vscode-extension'
                });

                if (response.data.status === 'success') {
                    this.outputChannel.appendLine('Workspace registered successfully');

                    // Get project summary if available
                    const summaryResponse = await this.httpClient.get('/api/workspace/summary?client_id=vscode-extension');
                    if (summaryResponse.data.summary) {
                        this.outputChannel.appendLine('Project Analysis:');
                        this.outputChannel.appendLine(summaryResponse.data.summary);
                    }
                } else {
                    this.outputChannel.appendLine(`Workspace registration failed: ${response.data.error || 'Unknown error'}`);
                }
            } else {
                this.outputChannel.appendLine('No workspace folder detected - some features may be limited');
            }
        } catch (error: any) {
            this.outputChannel.appendLine(`Error registering workspace: ${error.message}`);
            // Don't throw - workspace registration is optional
        }
    }

    /**
     * Disconnect from the bridge
     */
    public disconnect(): void {
        this.outputChannel.appendLine('Disconnecting from AI Coding Agent bridge');

        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }

        this.currentSessionId = null;
        this.statusCallback('disconnected');
    }

    /**
     * Initialize WebSocket connection for real-time updates
     */
    private async initializeWebSocket(): Promise<void> {
        return new Promise((resolve, reject) => {
            try {
                this.socket = io(this.baseUrl, {
                    transports: ['websocket', 'polling'],
                    timeout: 30000 // Increased WebSocket timeout
                });

                this.socket.on('connect', () => {
                    this.outputChannel.appendLine('WebSocket connected');
                    resolve();
                });

                this.socket.on('disconnect', () => {
                    this.outputChannel.appendLine('WebSocket disconnected');
                    this.statusCallback('disconnected');
                });

                this.socket.on('connect_error', (error: any) => {
                    this.outputChannel.appendLine(`WebSocket connection error: ${error.message}`);
                    reject(error);
                });

                this.socket.on('session_event', (data: any) => {
                    this.handleSessionEvent(data);
                });

                this.socket.on('trajectory_updated', (data: any) => {
                    this.handleTrajectoryUpdate(data);
                });

                this.socket.on('session_output', (data: any) => {
                    this.handleSessionOutput(data);
                });

                // Listen for subscription confirmations
                this.socket.on('output_subscribed', (data: any) => {
                    this.outputChannel.appendLine(`Output subscription confirmed: ${JSON.stringify(data)}`);
                });

                this.socket.on('output_unsubscribed', (data: any) => {
                    this.outputChannel.appendLine(`Output unsubscription confirmed: ${JSON.stringify(data)}`);
                });

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Handle session events from WebSocket
     */
    private handleSessionEvent(data: any): void {
        this.outputChannel.appendLine(`Session Event: ${JSON.stringify(data)}`);

        if (data.session_id === this.currentSessionId) {
            switch (data.event_type) {
                case 'session_started':
                    this.statusCallback('running');
                    break;
                case 'session_completed':
                    this.statusCallback('connected');
                    this.currentSessionId = null;
                    break;
                case 'session_failed':
                    this.statusCallback('error');
                    this.currentSessionId = null;
                    break;
            }
        }
    }

    /**
     * Handle trajectory updates from WebSocket
     */
    private handleTrajectoryUpdate(data: any): void {
        this.outputChannel.appendLine(`Trajectory Update: ${JSON.stringify(data)}`);
    }

    /**
     * Handle session output from WebSocket
     */
    private handleSessionOutput(data: any): void {
        this.outputChannel.appendLine(`Session Output: ${JSON.stringify(data)}`);

        // Call registered callback for this session
        if (data.session_id && this.sessionOutputCallbacks.has(data.session_id)) {
            const callback = this.sessionOutputCallbacks.get(data.session_id);
            if (callback) {
                try {
                    callback(data);
                } catch (error) {
                    this.outputChannel.appendLine(`Error in session output callback: ${error}`);
                }
            }
        } else {
            this.outputChannel.appendLine(`No callback registered for session: ${data.session_id}`);
        }
    }

    /**
     * Subscribe to session output updates
     *
     * @param sessionId Session ID to subscribe to
     * @param callback Callback function for output updates
     */
    public subscribeToSessionOutput(sessionId: string, callback: (data: any) => void): void {
        this.sessionOutputCallbacks.set(sessionId, callback);

        // Subscribe to session output via WebSocket (using correct event name)
        if (this.socket) {
            this.socket.emit('subscribe_output', { session_id: sessionId });
            this.outputChannel.appendLine(`Subscribed to output for session: ${sessionId}`);
        }
    }

    /**
     * Unsubscribe from session output updates
     *
     * @param sessionId Session ID to unsubscribe from
     */
    public unsubscribeFromSessionOutput(sessionId: string): void {
        this.sessionOutputCallbacks.delete(sessionId);

        // Unsubscribe from session output via WebSocket (using correct event name)
        if (this.socket) {
            this.socket.emit('unsubscribe_output', { session_id: sessionId });
            this.outputChannel.appendLine(`Unsubscribed from output for session: ${sessionId}`);
        }
    }

    /**
     * Run the AI Coding Agent in continuous mode
     *
     * @param problemStatement Problem statement to solve
     * @param repoPath Path to the repository
     * @param modelName Name of the model to use
     * @param continuousMode Whether to enable continuous execution
     * @param enableParallel Whether to enable parallel sub-tasks
     * @returns Promise with the result
     */
    public async runAgentContinuous(
        problemStatement: string,
        repoPath: string,
        modelName: string,
        continuousMode: boolean = true,
        enableParallel: boolean = false
    ): Promise<ApiResponse> {
        this.outputChannel.appendLine(`Running AI Coding Agent in continuous mode with task: ${problemStatement}`);
        this.outputChannel.appendLine(`Repository: ${repoPath}`);
        this.outputChannel.appendLine(`Model: ${modelName}`);
        this.outputChannel.appendLine(`Continuous Mode: ${continuousMode}`);
        this.outputChannel.appendLine(`Parallel Tasks: ${enableParallel}`);

        try {
            const sessionConfig: SessionConfig = {
                problem_statement: problemStatement,
                repo_path: repoPath,
                model_name: modelName,
                continuous_mode: continuousMode,
                enable_parallel: enableParallel,
                tools: {
                    execution_timeout: 600, // 10 minutes per command
                    total_execution_timeout: 0, // Unlimited
                    max_consecutive_execution_timeouts: 10
                },
                environment: {
                    continuous_mode: true,
                    auto_resume: true
                }
            };

            // Create a session using the new local SWE-Agent endpoint
            const response = await this.httpClient.post<ApiResponse>('/api/sessions', sessionConfig);

            if (response.data.status === 'created' && response.data.session_id) {
                this.currentSessionId = response.data.session_id;
                this.outputChannel.appendLine(`Created session: ${this.currentSessionId}`);

                // Start the session using local SWE-Agent with git bypass
                const startResponse = await this.httpClient.post<ApiResponse>(
                    `/api/sessions/${this.currentSessionId}/start-local`
                );

                if (startResponse.data.status === 'success') {
                    this.statusCallback('running');
                    this.outputChannel.appendLine('Local SWE-Agent started successfully');

                    // Subscribe to session updates via WebSocket
                    if (this.socket) {
                        this.socket.emit('subscribe_session', { session_id: this.currentSessionId });
                    }

                    return { status: 'success', session_id: this.currentSessionId };
                } else {
                    throw new Error(startResponse.data.error || 'Failed to start local SWE-Agent session');
                }
            } else {
                throw new Error(response.data.error || 'Failed to create session');
            }

        } catch (error: any) {
            this.outputChannel.appendLine(`Error running AI Coding Agent in continuous mode: ${error.message}`);
            this.statusCallback('error');

            if (error.response?.data?.error) {
                throw new Error(error.response.data.error);
            } else {
                throw error;
            }
        }
    }

    /**
     * Run the AI Coding Agent
     *
     * @param problemStatement Problem statement to solve
     * @param repoPath Path to the repository
     * @param modelName Name of the model to use
     * @returns Promise with the result
     */
    public async runAgent(problemStatement: string, repoPath: string, modelName: string): Promise<ApiResponse> {
        this.outputChannel.appendLine(`Running AI Coding Agent with task: ${problemStatement}`);
        this.outputChannel.appendLine(`Repository: ${repoPath}`);
        this.outputChannel.appendLine(`Model: ${modelName}`);

        try {
            const sessionConfig: SessionConfig = {
                problem_statement: problemStatement,
                repo_path: repoPath,
                model_name: modelName
            };

            // Create a new session
            const response = await this.httpClient.post<ApiResponse>('/api/sessions', sessionConfig);

            if (response.data.status === 'created' && response.data.session_id) {
                this.currentSessionId = response.data.session_id;
                this.outputChannel.appendLine(`Created session: ${this.currentSessionId}`);

                // Start the session using local SWE-Agent with git bypass
                const startResponse = await this.httpClient.post<ApiResponse>(
                    `/api/sessions/${this.currentSessionId}/start-local`
                );

                if (startResponse.data.status === 'success') {
                    this.statusCallback('running');
                    this.outputChannel.appendLine('Local SWE-Agent started successfully');

                    // Subscribe to session updates via WebSocket
                    if (this.socket) {
                        this.socket.emit('subscribe_session', { session_id: this.currentSessionId });
                    }

                    return { status: 'success', session_id: this.currentSessionId };
                } else {
                    throw new Error(startResponse.data.error || 'Failed to start local SWE-Agent session');
                }
            } else {
                throw new Error(response.data.error || 'Failed to create session');
            }

        } catch (error: any) {
            this.outputChannel.appendLine(`Error running AI Coding Agent: ${error.message}`);
            this.statusCallback('error');

            if (error.response?.data?.error) {
                throw new Error(error.response.data.error);
            } else {
                throw error;
            }
        }
    }

    /**
     * Stop the AI Coding Agent
     *
     * @returns Promise with the result
     */
    public async stopAgent(): Promise<ApiResponse> {
        this.outputChannel.appendLine('Stopping AI Coding Agent...');

        try {
            if (!this.currentSessionId) {
                return { status: 'success', message: 'No active session to stop' };
            }

            const response = await this.httpClient.post<ApiResponse>(
                `/api/sessions/${this.currentSessionId}/stop`
            );

            if (response.data.status === 'success') {
                this.outputChannel.appendLine('AI Coding Agent stopped successfully');
                this.statusCallback('connected');
                this.currentSessionId = null;
                return response.data;
            } else {
                throw new Error(response.data.error || 'Failed to stop session');
            }

        } catch (error: any) {
            this.outputChannel.appendLine(`Error stopping AI Coding Agent: ${error.message}`);
            this.statusCallback('error');

            if (error.response?.data?.error) {
                throw new Error(error.response.data.error);
            } else {
                throw error;
            }
        }
    }

    /**
     * Check the status of the AI Coding Agent bridge
     *
     * @returns Promise with the result
     */
    public async checkStatus(): Promise<ApiResponse> {
        this.outputChannel.appendLine('Checking AI Coding Agent bridge status...');

        try {
            const response = await this.httpClient.get<ApiResponse>('/health');

            if (response.data.status === 'ok') {
                this.outputChannel.appendLine('AI Coding Agent bridge is healthy');
                return response.data;
            } else {
                throw new Error('Bridge health check failed');
            }

        } catch (error: any) {
            this.outputChannel.appendLine(`Bridge health check failed: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get current session information
     *
     * @returns Current session ID or null
     */
    public getCurrentSessionId(): string | null {
        return this.currentSessionId;
    }

    /**
     * Get session details
     *
     * @param sessionId Session ID to get details for
     * @returns Promise with session details
     */
    public async getSessionDetails(sessionId?: string): Promise<ApiResponse> {
        const id = sessionId || this.currentSessionId;
        if (!id) {
            throw new Error('No session ID provided');
        }

        try {
            const response = await this.httpClient.get<ApiResponse>(`/api/sessions/${id}`);
            return response.data;
        } catch (error: any) {
            this.outputChannel.appendLine(`Error getting session details: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get session trajectory
     *
     * @param sessionId Session ID to get trajectory for
     * @returns Promise with trajectory data
     */
    public async getSessionTrajectory(sessionId?: string): Promise<ApiResponse> {
        const id = sessionId || this.currentSessionId;
        if (!id) {
            throw new Error('No session ID provided');
        }

        try {
            const response = await this.httpClient.get<ApiResponse>(`/api/sessions/${id}/trajectory`);
            return response.data;
        } catch (error: any) {
            this.outputChannel.appendLine(`Error getting session trajectory: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get workspace summary
     *
     * @param clientId Client ID for workspace context
     * @returns Promise with workspace summary
     */
    public async getWorkspaceSummary(clientId: string = 'vscode-extension'): Promise<any> {
        try {
            const response = await this.httpClient.get(`/api/workspace/summary?client_id=${clientId}`);
            return response.data;
        } catch (error: any) {
            this.outputChannel.appendLine(`Error getting workspace summary: ${error.message}`);
            throw error;
        }
    }

    /**
     * Create a chat session
     *
     * @param title Optional title for the chat session
     * @param context Optional context information
     * @returns Promise with chat session details
     */
    public async createChatSession(title?: string, context?: any): Promise<ChatSession> {
        this.outputChannel.appendLine('Creating chat session...');

        try {
            const payload: any = {};
            if (title) {
                payload.title = title;
            }
            if (context) {
                payload.context = context;
            }

            const response = await this.httpClient.post<any>('/api/chat/sessions', payload);

            if (response.data.status === 'success' && response.data.session_id) {
                this.outputChannel.appendLine(`Created chat session: ${response.data.session_id}`);
                return {
                    session_id: response.data.session_id,
                    title: title || 'Chat Session',
                    status: 'active',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };
            } else {
                throw new Error(response.data.error || 'Failed to create chat session');
            }

        } catch (error: any) {
            this.outputChannel.appendLine(`Error creating chat session: ${error.message}`);
            throw error;
        }
    }

    /**
     * Generic request method for API calls
     */
    public async request(method: string, endpoint: string, data?: any): Promise<any> {
        try {
            const response = await this.httpClient.request({
                method: method.toLowerCase() as any,
                url: endpoint,
                data
            });

            return response.data;
        } catch (error: any) {
            this.outputChannel.appendLine(`Request failed: ${error.message}`);
            throw error;
        }
    }

    /**
     * Update base URL for the bridge client
     */
    public updateBaseUrl(newBaseUrl: string): void {
        this.baseUrl = newBaseUrl;
        this.httpClient.defaults.baseURL = newBaseUrl;
        this.outputChannel.appendLine(`Bridge client base URL updated to: ${newBaseUrl}`);
    }

    /**
     * Send a message to a chat session
     *
     * @param sessionId Chat session ID
     * @param message Message to send
     * @param context Optional context information
     * @returns Promise with response
     */
    public async sendChatMessage(sessionId: string, message: string, context?: any): Promise<ApiResponse> {
        this.outputChannel.appendLine(`Sending chat message to session ${sessionId}: ${message}`);

        try {
            const payload: any = {
                message: message
            };
            if (context) {
                payload.context = context;
            }

            const response = await this.httpClient.post<ApiResponse>(
                `/api/chat/sessions/${sessionId}/messages`,
                payload
            );

            return response.data;

        } catch (error: any) {
            this.outputChannel.appendLine(`Error sending chat message: ${error.message}`);
            throw error;
        }
    }

    /**
     * Join a chat session for real-time updates
     *
     * @param sessionId Chat session ID
     * @param onMessage Callback for incoming messages
     * @param onChunk Callback for streaming chunks
     * @param onComplete Callback for completion
     * @param onError Callback for errors
     */
    public joinChatSession(
        sessionId: string,
        onMessage?: (data: any) => void,
        onChunk?: (data: any) => void,
        onComplete?: (data: any) => void,
        onError?: (error: any) => void
    ): void {
        if (!this.socket) {
            throw new Error('WebSocket not connected');
        }

        this.outputChannel.appendLine(`Joining chat session: ${sessionId}`);

        // Join the chat session
        this.socket.emit('chat_join', { session_id: sessionId });

        // Set up event listeners
        this.socket.on('chat_joined', (data: any) => {
            this.outputChannel.appendLine(`Joined chat session: ${data.session_id}`);
        });

        this.socket.on('chat_chunk', (data: any) => {
            if (data.session_id === sessionId && onChunk) {
                onChunk(data);
            }
        });

        this.socket.on('chat_complete', (data: any) => {
            if (data.session_id === sessionId && onComplete) {
                onComplete(data);
            }
        });

        this.socket.on('chat_error', (error: any) => {
            if (onError) {
                onError(error);
            }
        });
    }

    /**
     * Send a chat message via WebSocket for real-time streaming
     *
     * @param sessionId Chat session ID
     * @param message Message to send
     * @param context Optional context information
     */
    public sendChatMessageStream(sessionId: string, message: string, context?: any): void {
        if (!this.socket) {
            throw new Error('WebSocket not connected');
        }

        this.outputChannel.appendLine(`Sending streaming chat message to session ${sessionId}: ${message}`);

        const payload: any = {
            session_id: sessionId,
            message: message
        };
        if (context) {
            payload.context = context;
        }

        this.socket.emit('chat_message', payload);
    }

    // File Operations Methods

    /**
     * Read file content
     *
     * @param filePath Path to the file
     * @param encoding File encoding (optional, defaults to utf-8)
     * @returns Promise with file content and metadata
     */
    public async readFile(filePath: string, encoding?: string): Promise<ApiResponse> {
        this.outputChannel.appendLine(`Reading file: ${filePath}`);

        try {
            const response = await this.httpClient.post<ApiResponse>('/api/files/read', {
                file_path: filePath,
                encoding: encoding
            });

            return response.data;

        } catch (error: any) {
            this.outputChannel.appendLine(`Error reading file: ${error.message}`);
            throw error;
        }
    }

    /**
     * Write content to a file
     *
     * @param filePath Path to the file
     * @param content File content
     * @param encoding File encoding (optional, defaults to utf-8)
     * @param createDirs Create parent directories if needed (optional)
     * @returns Promise with operation result
     */
    public async writeFile(filePath: string, content: string, encoding?: string, createDirs?: boolean): Promise<ApiResponse> {
        this.outputChannel.appendLine(`Writing file: ${filePath}`);

        try {
            const response = await this.httpClient.post<ApiResponse>('/api/files/write', {
                file_path: filePath,
                content: content,
                encoding: encoding,
                create_dirs: createDirs
            });

            return response.data;

        } catch (error: any) {
            this.outputChannel.appendLine(`Error writing file: ${error.message}`);
            throw error;
        }
    }

    /**
     * Create a new file
     *
     * @param filePath Path to the file
     * @param content Initial file content (optional)
     * @param encoding File encoding (optional, defaults to utf-8)
     * @param createDirs Create parent directories if needed (optional)
     * @param overwrite Whether to overwrite existing file (optional)
     * @returns Promise with operation result
     */
    public async createFile(filePath: string, content?: string, encoding?: string, createDirs?: boolean, overwrite?: boolean): Promise<ApiResponse> {
        this.outputChannel.appendLine(`Creating file: ${filePath}`);

        try {
            const response = await this.httpClient.post<ApiResponse>('/api/files/create', {
                file_path: filePath,
                content: content || '',
                encoding: encoding,
                create_dirs: createDirs,
                overwrite: overwrite
            });

            return response.data;

        } catch (error: any) {
            this.outputChannel.appendLine(`Error creating file: ${error.message}`);
            throw error;
        }
    }

    /**
     * Delete a file or directory
     *
     * @param filePath Path to the file or directory
     * @param recursive Whether to delete directories recursively (optional)
     * @returns Promise with operation result
     */
    public async deleteFile(filePath: string, recursive?: boolean): Promise<ApiResponse> {
        this.outputChannel.appendLine(`Deleting file: ${filePath}`);

        try {
            const response = await this.httpClient.post<ApiResponse>('/api/files/delete', {
                file_path: filePath,
                recursive: recursive
            });

            return response.data;

        } catch (error: any) {
            this.outputChannel.appendLine(`Error deleting file: ${error.message}`);
            throw error;
        }
    }

    /**
     * Rename or move a file/directory
     *
     * @param oldPath Current path
     * @param newPath New path
     * @param createDirs Create parent directories if needed (optional)
     * @returns Promise with operation result
     */
    public async renameFile(oldPath: string, newPath: string, createDirs?: boolean): Promise<ApiResponse> {
        this.outputChannel.appendLine(`Renaming file: ${oldPath} -> ${newPath}`);

        try {
            const response = await this.httpClient.post<ApiResponse>('/api/files/rename', {
                old_path: oldPath,
                new_path: newPath,
                create_dirs: createDirs
            });

            return response.data;

        } catch (error: any) {
            this.outputChannel.appendLine(`Error renaming file: ${error.message}`);
            throw error;
        }
    }

    /**
     * List directory contents
     *
     * @param directoryPath Path to the directory
     * @param includeHidden Include hidden files (optional)
     * @param recursive Recursive listing (optional)
     * @param maxDepth Maximum recursion depth (optional)
     * @returns Promise with directory listing
     */
    public async listDirectory(directoryPath: string, includeHidden?: boolean, recursive?: boolean, maxDepth?: number): Promise<ApiResponse> {
        this.outputChannel.appendLine(`Listing directory: ${directoryPath}`);

        try {
            const response = await this.httpClient.post<ApiResponse>('/api/files/list', {
                directory_path: directoryPath,
                include_hidden: includeHidden,
                recursive: recursive,
                max_depth: maxDepth
            });

            return response.data;

        } catch (error: any) {
            this.outputChannel.appendLine(`Error listing directory: ${error.message}`);
            throw error;
        }
    }

    // Terminal Operations Methods

    /**
     * Execute a single command
     *
     * @param command Command to execute
     * @param workingDirectory Working directory (optional)
     * @param timeout Timeout in seconds (optional)
     * @param captureOutput Whether to capture output (optional)
     * @returns Promise with execution result
     */
    public async executeCommand(command: string, workingDirectory?: string, timeout?: number, captureOutput?: boolean): Promise<ApiResponse> {
        this.outputChannel.appendLine(`Executing command: ${command}`);

        try {
            const response = await this.httpClient.post<ApiResponse>('/api/terminal/execute', {
                command: command,
                working_directory: workingDirectory,
                timeout: timeout,
                capture_output: captureOutput
            });

            return response.data;

        } catch (error: any) {
            this.outputChannel.appendLine(`Error executing command: ${error.message}`);
            throw error;
        }
    }

    /**
     * Create a new terminal session
     *
     * @param workingDirectory Working directory (optional)
     * @param sessionName Session name (optional)
     * @returns Promise with session information
     */
    public async createTerminalSession(workingDirectory?: string, sessionName?: string): Promise<ApiResponse> {
        this.outputChannel.appendLine(`Creating terminal session`);

        try {
            const response = await this.httpClient.post<ApiResponse>('/api/terminal/sessions', {
                working_directory: workingDirectory,
                session_name: sessionName
            });

            return response.data;

        } catch (error: any) {
            this.outputChannel.appendLine(`Error creating terminal session: ${error.message}`);
            throw error;
        }
    }

    /**
     * Send input to a terminal session
     *
     * @param sessionId Terminal session ID
     * @param input Input to send
     * @returns Promise with operation result
     */
    public async sendTerminalInput(sessionId: string, input: string): Promise<ApiResponse> {
        this.outputChannel.appendLine(`Sending input to terminal session ${sessionId}: ${input}`);

        try {
            const response = await this.httpClient.post<ApiResponse>(`/api/terminal/sessions/${sessionId}/input`, {
                input: input
            });

            return response.data;

        } catch (error: any) {
            this.outputChannel.appendLine(`Error sending terminal input: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get output from a terminal session
     *
     * @param sessionId Terminal session ID
     * @param sinceTimestamp Get output since timestamp (optional)
     * @param format Output format ('json' or 'text', optional)
     * @returns Promise with terminal output
     */
    public async getTerminalOutput(sessionId: string, sinceTimestamp?: number, format?: string): Promise<ApiResponse | string> {
        this.outputChannel.appendLine(`Getting output from terminal session ${sessionId}`);

        try {
            const params = new URLSearchParams();
            if (sinceTimestamp) {
                params.append('since', sinceTimestamp.toString());
            }
            if (format) {
                params.append('format', format);
            }

            const response = await this.httpClient.get(`/api/terminal/sessions/${sessionId}/output?${params.toString()}`);

            return response.data;

        } catch (error: any) {
            this.outputChannel.appendLine(`Error getting terminal output: ${error.message}`);
            throw error;
        }
    }

    /**
     * Delete a terminal session
     *
     * @param sessionId Terminal session ID
     * @returns Promise with operation result
     */
    public async deleteTerminalSession(sessionId: string): Promise<ApiResponse> {
        this.outputChannel.appendLine(`Deleting terminal session ${sessionId}`);

        try {
            const response = await this.httpClient.delete<ApiResponse>(`/api/terminal/sessions/${sessionId}`);

            return response.data;

        } catch (error: any) {
            this.outputChannel.appendLine(`Error deleting terminal session: ${error.message}`);
            throw error;
        }
    }

    /**
     * List all terminal sessions
     *
     * @returns Promise with sessions list
     */
    public async listTerminalSessions(): Promise<ApiResponse> {
        this.outputChannel.appendLine(`Listing terminal sessions`);

        try {
            const response = await this.httpClient.get<ApiResponse>('/api/terminal/sessions');

            return response.data;

        } catch (error: any) {
            this.outputChannel.appendLine(`Error listing terminal sessions: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get terminal session status
     *
     * @param sessionId Terminal session ID
     * @returns Promise with session status
     */
    public async getTerminalSessionStatus(sessionId: string): Promise<ApiResponse> {
        this.outputChannel.appendLine(`Getting terminal session status ${sessionId}`);

        try {
            const response = await this.httpClient.get<ApiResponse>(`/api/terminal/sessions/${sessionId}/status`);

            return response.data;

        } catch (error: any) {
            this.outputChannel.appendLine(`Error getting terminal session status: ${error.message}`);
            throw error;
        }
    }
}
