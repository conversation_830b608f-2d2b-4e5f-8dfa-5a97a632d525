import * as vscode from 'vscode';
import { BridgeClient } from './bridge-client';

interface ChatMessage {
    id: string;
    type: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: string;
    elements?: TechnicalElement[];
}

interface TechnicalElement {
    id: string;
    type: 'command' | 'code' | 'file_operation' | 'output' | 'terminal';
    title: string;
    preview?: string;
    content: string;
    language?: string;
    collapsed: boolean;
    status?: 'pending' | 'running' | 'completed' | 'error';
}

interface TerminalOutput {
    id: string;
    type: 'stdout' | 'stderr' | 'progress' | 'info' | 'error';
    content: string;
    timestamp: string;
    session_id: string;
}

interface ExecutionStep {
    id: string;
    title: string;
    status: 'pending' | 'running' | 'completed' | 'error';
    content?: string;
    timestamp: string;
}

interface ExecutionBlock {
    id: string;
    title: string;
    status: 'pending' | 'running' | 'completed' | 'error';
    steps: ExecutionStep[];
    collapsed: boolean;
    timestamp: string;
}

export class ExecutionView {
    private panel: vscode.WebviewPanel | undefined;
    private bridgeClient: BridgeClient;
    private context: vscode.ExtensionContext;
    private chatMessages: ChatMessage[] = [];
    private currentChatSessionId: string | null = null;
    private isProcessing: boolean = false;
    private terminalOutputs: TerminalOutput[] = [];
    private currentTerminalElementId: string | null = null;

    constructor(context: vscode.ExtensionContext, bridgeClient: BridgeClient) {
        this.context = context;
        this.bridgeClient = bridgeClient;
    }

    /**
     * Show the execution view
     */
    public show() {
        if (this.panel) {
            this.panel.reveal(vscode.ViewColumn.Beside);
            return;
        }

        this.panel = vscode.window.createWebviewPanel(
            'aiCodingAgentExecution',
            'AI Coding Agent',
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [this.context.extensionUri]
            }
        );

        this.panel.webview.html = this.getHtmlForWebview();

        // Handle messages from the webview
        this.panel.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.command) {
                    case 'sendMessage':
                        await this.sendMessage(message.text);
                        break;
                    case 'toggleElement':
                        this.toggleTechnicalElement(message.messageId, message.elementId);
                        break;
                    case 'clearHistory':
                        this.clearHistory();
                        break;
                }
            },
            undefined,
            this.context.subscriptions
        );

        this.panel.onDidDispose(
            () => {
                this.panel = undefined;
            },
            null,
            this.context.subscriptions
        );

        // Add initial welcome message with project analysis
        this.addWelcomeMessageWithProjectAnalysis();
    }

    /**
     * Send a message to the AI agent
     */
    private async sendMessage(text: string) {
        if (this.isProcessing) {
            vscode.window.showWarningMessage('Please wait for the current request to complete.');
            return;
        }

        this.isProcessing = true;

        try {
            await this.sendMessageNewUI(text);
        } finally {
            this.isProcessing = false;
            this.updateWebview();
        }
    }

    /**
     * Send message using new chat UI with SWE-Agent integration
     */
    private async sendMessageNewUI(text: string) {
        // Add user message
        this.addChatMessage('user', text);

        // Create or reuse SWE-Agent session
        if (!this.currentChatSessionId) {
            this.addChatMessage('system', 'Creating SWE-Agent session...');

            try {
                // Use SWE-Agent continuous mode for better task completion
                const workspaceContext = this.getWorkspaceContext();
                const workspacePath = workspaceContext.project_root || '/u/Arun Dev/Python Projects/AI-Coding-Agent';

                const response = await this.bridgeClient.runAgentContinuous(
                    text,
                    workspacePath,
                    'claude-3-5-sonnet-20241022',
                    true, // continuous mode
                    false // parallel tasks
                );

                if (response.status === 'success' && response.session_id) {
                    this.currentChatSessionId = response.session_id;
                    this.updateLastSystemMessage('Local SWE-Agent session created and started');

                    // Add progress tracking message with terminal output
                    const progressMessageId = this.addChatMessage('assistant', '🔄 Local SWE-Agent is analyzing your request...');

                    // Add terminal output element
                    this.currentTerminalElementId = this.addTerminalElement(progressMessageId, 'SWE-Agent Terminal Output');

                    // Subscribe to real-time session output
                    if (this.currentChatSessionId) {
                        console.log(`Subscribing to session output for: ${this.currentChatSessionId}`);
                        this.bridgeClient.subscribeToSessionOutput(this.currentChatSessionId, (data) => {
                            console.log('Received session output data:', data);
                            this.handleSessionOutput(data);
                        });
                    }

                    // Monitor session progress
                    this.monitorSessionProgress(progressMessageId);
                } else {
                    this.updateLastSystemMessage(`Error creating Local SWE-Agent session: ${response.error || 'Unknown error'}`);
                    return;
                }
            } catch (error: any) {
                this.updateLastSystemMessage(`Error creating SWE-Agent session: ${error.message}`);
                return;
            }
        } else {
            // Session exists, send additional message
            this.addChatMessage('system', 'Sending additional instruction to SWE-Agent...');

            try {
                // For existing sessions, we can send follow-up instructions
                const progressMessageId = this.addChatMessage('assistant', '🔄 Processing additional instruction...');

                // Get current session status and trajectory
                const sessionDetails = await this.bridgeClient.getSessionDetails(this.currentChatSessionId);

                if (sessionDetails.status === 'completed' || sessionDetails.status === 'failed') {
                    // Session ended, create new one
                    this.currentChatSessionId = null;
                    this.updateChatMessage(progressMessageId, '🔄 Previous session ended, creating new session...');
                    await this.sendMessageNewUI(text); // Recursive call to create new session
                    return;
                } else {
                    // Session is still active, show current progress
                    this.updateChatMessage(progressMessageId, `🔄 SWE-Agent is working... Status: ${sessionDetails.status}`);
                    this.monitorSessionProgress(progressMessageId);
                }
            } catch (error: any) {
                const errorMessageId = this.addChatMessage('assistant', `❌ Error: ${error.message}`);
            }
        }
    }

    /**
     * Handle real-time session output
     */
    private handleSessionOutput(data: any) {
        if (!this.currentTerminalElementId) {
            console.warn('No terminal element ID available for session output');
            return;
        }

        try {
            // Map bridge server data structure to our format
            const output: TerminalOutput = {
                id: `output_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                type: data.stream || data.type || 'stdout', // Bridge sends 'stream' field
                content: data.output || data.content || data.message || '', // Bridge sends 'output' field
                timestamp: data.timestamp ? new Date(data.timestamp).toLocaleTimeString() : new Date().toLocaleTimeString(),
                session_id: data.session_id
            };

            this.terminalOutputs.push(output);
            this.updateTerminalElement(this.currentTerminalElementId, this.terminalOutputs);

            // Log for debugging (remove in production)
            console.log('Session output processed:', {
                type: output.type,
                content: output.content.substring(0, 100),
                session_id: output.session_id
            });
        } catch (error) {
            console.error('Error handling session output:', error);
        }
    }

    /**
     * Add terminal element to a message
     */
    private addTerminalElement(messageId: string, title: string): string {
        const elementId = `terminal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // Add the terminal element with the specific ID
        const message = this.chatMessages.find(m => m.id === messageId);
        if (message) {
            const element: TechnicalElement = {
                id: elementId,
                type: 'terminal',
                title: title,
                preview: 'Real-time SWE-Agent output...',
                content: 'Waiting for output...',
                collapsed: false // Start expanded for terminal
            };

            message.elements = message.elements || [];
            message.elements.push(element);
            this.updateWebview();
        }

        return elementId;
    }

    /**
     * Update terminal element with new outputs
     */
    private updateTerminalElement(elementId: string, outputs: TerminalOutput[]) {
        try {
            // Find the specific terminal element by ID
            for (const message of this.chatMessages) {
                if (message.elements) {
                    const element = message.elements.find(e => e.id === elementId && e.type === 'terminal');
                    if (element) {
                        // Update content with latest outputs
                        const recentOutputs = outputs.slice(-100); // Show last 100 lines
                        const terminalContent = this.formatTerminalOutput(recentOutputs);

                        element.content = terminalContent;
                        element.preview = recentOutputs.length > 0 ?
                            `Latest: ${recentOutputs[recentOutputs.length - 1].content.substring(0, 60)}...` :
                            'Waiting for output...';

                        this.updateWebview();
                        return; // Found and updated
                    }
                }
            }
            console.warn(`Terminal element with ID ${elementId} not found`);
        } catch (error) {
            console.error('Error updating terminal element:', error);
        }
    }

    /**
     * Format terminal output with proper styling
     */
    private formatTerminalOutput(outputs: TerminalOutput[]): string {
        if (!outputs || outputs.length === 0) {
            return '<div class="terminal-line info"><span class="terminal-content">Waiting for output...</span></div>';
        }

        return outputs.map(output => {
            const timestamp = output.timestamp;
            const type = output.type;
            const content = this.escapeHtml(output.content || '');

            return `<div class="terminal-line ${type}">` +
                   `<span class="terminal-timestamp">[${timestamp}]</span> ` +
                   `<span class="terminal-type">${type.toUpperCase()}:</span> ` +
                   `<span class="terminal-content">${content}</span>` +
                   `</div>`;
        }).join('');
    }

    /**
     * Monitor SWE-Agent session progress
     */
    private async monitorSessionProgress(messageId: string) {
        if (!this.currentChatSessionId) return;

        const maxChecks = 120; // Monitor for up to 2 hours (120 * 60 seconds)
        let checkCount = 0;

        const checkProgress = async () => {
            try {
                const sessionDetails = await this.bridgeClient.getSessionDetails(this.currentChatSessionId!);
                const trajectory = await this.bridgeClient.getSessionTrajectory(this.currentChatSessionId!);

                checkCount++;
                const elapsed = Math.floor(checkCount * 60 / 60); // Convert to minutes

                if (sessionDetails.status === 'completed') {
                    this.updateChatMessage(messageId, `✅ Local SWE-Agent completed the task successfully! (${elapsed} minutes)`);

                    // Unsubscribe from session output
                    this.bridgeClient.unsubscribeFromSessionOutput(this.currentChatSessionId!);
                    this.currentTerminalElementId = null;

                    // Add trajectory as technical elements
                    if (trajectory.trajectory && trajectory.trajectory.length > 0) {
                        this.addTrajectoryElements(messageId, trajectory.trajectory);
                    }

                    return; // Stop monitoring
                } else if (sessionDetails.status === 'failed') {
                    this.updateChatMessage(messageId, `❌ Local SWE-Agent task failed after ${elapsed} minutes. Error: ${sessionDetails.error || 'Unknown error'}`);

                    // Unsubscribe from session output
                    this.bridgeClient.unsubscribeFromSessionOutput(this.currentChatSessionId!);
                    this.currentTerminalElementId = null;

                    return; // Stop monitoring
                } else if (sessionDetails.status === 'running') {
                    const progress = sessionDetails.progress || 0;
                    const currentStep = sessionDetails.current_step || 'Working...';
                    this.updateChatMessage(messageId, `🔄 Local SWE-Agent working... (${elapsed}m) Progress: ${Math.round(progress * 100)}% - ${currentStep}`);
                } else {
                    this.updateChatMessage(messageId, `ℹ️ Local SWE-Agent status: ${sessionDetails.status} (${elapsed}m)`);
                }

                // Continue monitoring if not completed and within time limit
                if (checkCount < maxChecks && sessionDetails.status === 'running') {
                    setTimeout(checkProgress, 30000); // Check every 30 seconds for more responsive updates
                } else if (checkCount >= maxChecks) {
                    this.updateChatMessage(messageId, `⏰ Monitoring timeout reached (2 hours). Session may still be running.`);
                    this.bridgeClient.unsubscribeFromSessionOutput(this.currentChatSessionId!);
                    this.currentTerminalElementId = null;
                }

            } catch (error: any) {
                this.updateChatMessage(messageId, `❌ Error monitoring session: ${error.message}`);
            }
        };

        // Start monitoring after a short delay
        setTimeout(checkProgress, 3000); // First check after 3 seconds
    }

    /**
     * Add trajectory elements to a message
     */
    private addTrajectoryElements(messageId: string, trajectory: any[]) {
        for (const step of trajectory) {
            if (step.action && step.action.command) {
                this.addTechnicalElement(messageId, 'command', 'Terminal Command', step.action.command, step.action.command);
            }
            if (step.observation && step.observation.content) {
                this.addTechnicalElement(messageId, 'output', 'Command Output',
                    step.observation.content.substring(0, 100) + '...',
                    step.observation.content);
            }
        }
    }



    /**
     * Add a chat message
     */
    private addChatMessage(type: 'user' | 'assistant' | 'system', content: string): string {
        const messageId = `msg_${Date.now()}`;
        const message: ChatMessage = {
            id: messageId,
            type: type,
            content: content,
            timestamp: new Date().toLocaleTimeString(),
            elements: []
        };

        this.chatMessages.push(message);
        this.updateWebview();
        return messageId;
    }

    /**
     * Update a chat message
     */
    private updateChatMessage(messageId: string, content: string) {
        const message = this.chatMessages.find(m => m.id === messageId);
        if (message) {
            message.content = content;
            this.updateWebview();
        }
    }

    /**
     * Update last system message
     */
    private updateLastSystemMessage(content: string) {
        const lastSystemMessage = [...this.chatMessages].reverse().find(m => m.type === 'system');
        if (lastSystemMessage) {
            lastSystemMessage.content = content;
            this.updateWebview();
        }
    }

    /**
     * Parse response and add technical elements
     */
    private parseAndAddTechnicalElements(messageId: string, content: string) {
        const message = this.chatMessages.find(m => m.id === messageId);
        if (!message) return;

        // Parse for code blocks
        const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
        let match;
        while ((match = codeBlockRegex.exec(content)) !== null) {
            const language = match[1] || 'text';
            const code = match[2];
            const preview = code.split('\n').slice(0, 3).join('\n') + (code.split('\n').length > 3 ? '\n...' : '');

            this.addTechnicalElement(messageId, 'code', `Code (${language})`, preview, code, language);
        }

        // Parse for terminal commands
        const commandRegex = /(?:^|\n)\$\s+(.+)/g;
        while ((match = commandRegex.exec(content)) !== null) {
            const command = match[1];
            this.addTechnicalElement(messageId, 'command', 'Terminal Command', command, command);
        }

        // Parse for file operations
        const fileOpRegex = /(?:created|modified|deleted|updated)\s+(?:file\s+)?[`']?([^\s`']+)[`']?/gi;
        while ((match = fileOpRegex.exec(content)) !== null) {
            const filePath = match[1];
            this.addTechnicalElement(messageId, 'file_operation', 'File Operation', filePath, `File: ${filePath}`);
        }
    }

    /**
     * Add technical element to a message
     */
    private addTechnicalElement(messageId: string, type: TechnicalElement['type'], title: string, preview: string, content: string, language?: string) {
        const message = this.chatMessages.find(m => m.id === messageId);
        if (!message) return;

        const element: TechnicalElement = {
            id: `elem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: type,
            title: title,
            preview: preview,
            content: content,
            language: language,
            collapsed: true
        };

        message.elements = message.elements || [];
        message.elements.push(element);
        this.updateWebview();
    }

    /**
     * Toggle technical element
     */
    private toggleTechnicalElement(messageId: string, elementId: string) {
        const message = this.chatMessages.find(m => m.id === messageId);
        if (!message || !message.elements) return;

        const element = message.elements.find(e => e.id === elementId);
        if (element) {
            element.collapsed = !element.collapsed;
            this.updateWebview();
        }
    }













    /**
     * Clear execution history
     */
    private clearHistory() {
        // Clean up session output subscription
        if (this.currentChatSessionId) {
            this.bridgeClient.unsubscribeFromSessionOutput(this.currentChatSessionId);
        }

        // Reset state
        this.chatMessages = [];
        this.currentChatSessionId = null;
        this.terminalOutputs = [];
        this.currentTerminalElementId = null;
        this.isProcessing = false;

        this.addWelcomeMessage();
        this.updateWebview();
    }

    /**
     * Add welcome message
     */
    private addWelcomeMessage() {
        this.addChatMessage('system', '🤖 Welcome to AI Coding Agent with SWE-Agent! I can analyze code, fix bugs, implement features, and complete complex coding tasks. Just describe what you need and I\'ll work until it\'s done!');
    }

    /**
     * Add welcome message with automatic project analysis
     */
    private async addWelcomeMessageWithProjectAnalysis() {
        // Add basic welcome message
        this.addWelcomeMessage();

        // Check if workspace is available
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            this.addChatMessage('system', '⚠️ No workspace folder detected. Please open a project folder to enable full AI Coding Agent capabilities.');
            return;
        }

        // Add project analysis message
        const analysisMessageId = this.addChatMessage('system', '🔍 Analyzing your project workspace...');

        try {
            // Get workspace summary from bridge
            const summaryResponse = await this.bridgeClient.getWorkspaceSummary('vscode-extension');

            if (summaryResponse && summaryResponse.summary) {
                // Update with project summary
                this.updateChatMessage(analysisMessageId, `📁 **Project Analysis Complete**\n\n${summaryResponse.summary}\n\n✨ **Ready to assist!** I now understand your project structure and can help with code analysis, debugging, feature implementation, and more. What would you like me to work on?`);
            } else {
                // Fallback to basic workspace info
                const workspaceContext = this.getWorkspaceContext();
                const basicInfo = `📁 **Workspace Detected**\n\n• Project: ${workspaceContext.workspace_name}\n• Path: \`${workspaceContext.project_root}\`\n\n✨ **Ready to assist!** What would you like me to work on?`;
                this.updateChatMessage(analysisMessageId, basicInfo);
            }
        } catch (error: any) {
            // Error getting project analysis
            const workspaceContext = this.getWorkspaceContext();
            const errorInfo = `📁 **Workspace Detected** (Analysis failed)\n\n• Project: ${workspaceContext.workspace_name}\n• Path: \`${workspaceContext.project_root}\`\n• Error: ${error.message}\n\n✨ **Ready to assist!** What would you like me to work on?`;
            this.updateChatMessage(analysisMessageId, errorInfo);
        }
    }

    /**
     * Get workspace context
     */
    private getWorkspaceContext(): any {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) return {};

        const activeEditor = vscode.window.activeTextEditor;
        const context: any = {
            project_root: workspaceFolders[0].uri.fsPath,
            workspace_name: workspaceFolders[0].name,
            capabilities: {
                file_operations: true,
                terminal_execution: true,
                codebase_analysis: true,
                project_navigation: true
            }
        };

        if (activeEditor) {
            context.current_file = {
                path: activeEditor.document.uri.fsPath,
                relative_path: vscode.workspace.asRelativePath(activeEditor.document.uri),
                language: activeEditor.document.languageId,
                is_dirty: activeEditor.document.isDirty,
                line_count: activeEditor.document.lineCount
            };
        }

        return context;
    }

    /**
     * Update the webview content
     */
    private updateWebview() {
        if (this.panel) {
            this.panel.webview.html = this.getHtmlForWebview();
        }
    }

    /**
     * Get HTML for the webview
     */
    private getHtmlForWebview(): string {
        const contentHtml = this.renderChatUI();

        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>AI Coding Agent with SWE-Agent</title>
                <style>
                    ${this.getStyles()}
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>🤖 AI Coding Agent with SWE-Agent</h2>
                    <div class="session-info">
                        ${this.currentChatSessionId ? `SWE-Agent Session: ${this.currentChatSessionId.substring(0, 8)}...` : 'No active session'}
                        <button onclick="clearHistory()" class="clear-btn">🗑️ Clear</button>
                    </div>
                </div>

                ${contentHtml}

                <div class="input-container">
                    <textarea id="messageInput" placeholder="Describe your coding task and I'll complete it using SWE-Agent..." rows="3"></textarea>
                    <button onclick="sendMessage()" id="sendBtn" ${this.isProcessing ? 'disabled' : ''}>
                        ${this.isProcessing ? '⏳ SWE-Agent Working...' : '🚀 Start SWE-Agent'}
                    </button>
                </div>

                <script>
                    ${this.getScript()}
                </script>
            </body>
            </html>
        `;
    }

    /**
     * Render chat UI
     */
    private renderChatUI(): string {
        const messagesHtml = this.chatMessages.map(message => this.renderChatMessage(message)).join('');

        return `
            <div class="chat-container">
                ${messagesHtml}
            </div>
        `;
    }



    /**
     * Render a chat message
     */
    private renderChatMessage(message: ChatMessage): string {
        const elementsHtml = message.elements ? message.elements.map(element => this.renderTechnicalElement(message.id, element)).join('') : '';

        return `
            <div class="chat-message ${message.type}" data-message-id="${message.id}">
                <div class="message-header">
                    <span class="message-sender">${this.getSenderIcon(message.type)} ${this.getSenderName(message.type)}</span>
                    <span class="message-time">${message.timestamp}</span>
                </div>
                <div class="message-content">
                    <div class="message-text">${this.formatMessageContent(message.content)}</div>
                    ${elementsHtml}
                </div>
            </div>
        `;
    }

    /**
     * Render technical element
     */
    private renderTechnicalElement(messageId: string, element: TechnicalElement): string {
        const toggleIcon = element.collapsed ? '▶' : '▼';
        const typeIcon = this.getTechnicalElementIcon(element.type);

        // Special handling for terminal elements
        if (element.type === 'terminal') {
            return `
                <div class="technical-element ${element.type}" data-element-id="${element.id}">
                    <div class="element-header" onclick="toggleElement('${messageId}', '${element.id}')">
                        <span class="toggle-icon">${toggleIcon}</span>
                        <span class="element-icon">${typeIcon}</span>
                        <span class="element-title">${this.escapeHtml(element.title)}</span>
                        ${element.preview ? `<span class="element-preview">${this.escapeHtml(element.preview)}</span>` : ''}
                    </div>
                    <div class="element-content ${element.collapsed ? 'collapsed' : ''}">
                        <div class="element-code terminal-output">${element.content}</div>
                    </div>
                </div>
            `;
        }

        // Regular technical elements
        return `
            <div class="technical-element ${element.type}" data-element-id="${element.id}">
                <div class="element-header" onclick="toggleElement('${messageId}', '${element.id}')">
                    <span class="toggle-icon">${toggleIcon}</span>
                    <span class="element-icon">${typeIcon}</span>
                    <span class="element-title">${this.escapeHtml(element.title)}</span>
                    ${element.preview ? `<span class="element-preview">${this.escapeHtml(element.preview)}</span>` : ''}
                </div>
                <div class="element-content ${element.collapsed ? 'collapsed' : ''}">
                    <pre class="element-code ${element.language || ''}">${this.escapeHtml(element.content)}</pre>
                </div>
            </div>
        `;
    }

    /**
     * Get sender icon
     */
    private getSenderIcon(type: string): string {
        switch (type) {
            case 'user': return '👤';
            case 'assistant': return '🤖';
            case 'system': return '⚙️';
            default: return '💬';
        }
    }

    /**
     * Get sender name
     */
    private getSenderName(type: string): string {
        switch (type) {
            case 'user': return 'You';
            case 'assistant': return 'AI Assistant';
            case 'system': return 'System';
            default: return 'Unknown';
        }
    }

    /**
     * Get technical element icon
     */
    private getTechnicalElementIcon(type: string): string {
        switch (type) {
            case 'command': return '💻';
            case 'code': return '📝';
            case 'file_operation': return '📁';
            case 'output': return '📄';
            case 'terminal': return '🖥️';
            default: return '🔧';
        }
    }

    /**
     * Format message content
     */
    private formatMessageContent(content: string): string {
        // Remove code blocks and commands that will be shown as technical elements
        let formatted = content
            .replace(/```[\w]*\n[\s\S]*?```/g, '') // Remove code blocks
            .replace(/(?:^|\n)\$\s+.+/g, '') // Remove terminal commands
            .trim();

        // Convert markdown-style formatting
        formatted = formatted
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
            .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic
            .replace(/`(.*?)`/g, '<code>$1</code>') // Inline code
            .replace(/\n/g, '<br>'); // Line breaks

        return formatted || content; // Fallback to original if empty
    }





    /**
     * Escape HTML
     */
    private escapeHtml(text: string): string {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    /**
     * Get CSS styles
     */
    private getStyles(): string {
        return `
            body {
                font-family: var(--vscode-font-family);
                font-size: var(--vscode-font-size);
                color: var(--vscode-foreground);
                background-color: var(--vscode-editor-background);
                margin: 0;
                padding: 0;
                height: 100vh;
                display: flex;
                flex-direction: column;
            }

            .header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 16px;
                background-color: var(--vscode-titleBar-activeBackground);
                border-bottom: 1px solid var(--vscode-panel-border);
                flex-shrink: 0;
            }

            .header h2 {
                margin: 0;
                color: var(--vscode-titleBar-activeForeground);
                font-size: 16px;
                font-weight: 600;
            }

            .session-info {
                display: flex;
                align-items: center;
                gap: 12px;
                font-size: 12px;
                color: var(--vscode-descriptionForeground);
            }

            .clear-btn {
                background: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border: none;
                padding: 4px 8px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 11px;
            }

            .clear-btn:hover {
                background: var(--vscode-button-hoverBackground);
            }

            .execution-container {
                flex: 1;
                overflow-y: auto;
                padding: 16px;
            }

            .execution-block {
                margin-bottom: 16px;
                border: 1px solid var(--vscode-panel-border);
                border-radius: 8px;
                background-color: var(--vscode-editorWidget-background);
                overflow: hidden;
            }

            .execution-block.completed {
                border-color: var(--vscode-testing-iconPassed);
            }

            .execution-block.error {
                border-color: var(--vscode-errorForeground);
            }

            .execution-block.running {
                border-color: var(--vscode-progressBar-foreground);
            }

            .block-header {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                background: linear-gradient(135deg, var(--vscode-button-background), var(--vscode-button-hoverBackground));
                color: var(--vscode-button-foreground);
                cursor: pointer;
                transition: background-color 0.2s ease;
            }

            .block-header:hover {
                background: var(--vscode-button-hoverBackground);
            }

            .toggle-icon {
                margin-right: 8px;
                font-size: 12px;
                transition: transform 0.2s ease;
            }

            .block-title {
                flex: 1;
                font-weight: 500;
                font-size: 14px;
            }

            .block-status {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                margin-right: 8px;
            }

            .block-status.pending {
                background-color: var(--vscode-charts-yellow);
            }

            .block-status.running {
                background-color: var(--vscode-progressBar-foreground);
                animation: pulse 2s infinite;
            }

            .block-status.completed {
                background-color: var(--vscode-testing-iconPassed);
            }

            .block-status.error {
                background-color: var(--vscode-errorForeground);
            }

            .block-time {
                font-size: 11px;
                opacity: 0.7;
            }

            .block-content {
                max-height: 1000px;
                overflow: hidden;
                transition: max-height 0.3s ease;
            }

            .block-content.collapsed {
                max-height: 0;
            }

            .execution-step {
                padding: 12px 16px;
                border-bottom: 1px solid var(--vscode-panel-border);
            }

            .execution-step:last-child {
                border-bottom: none;
            }

            .execution-step.running {
                background-color: rgba(255, 165, 0, 0.1);
                border-left: 3px solid var(--vscode-progressBar-foreground);
            }

            .execution-step.completed {
                background-color: rgba(0, 255, 0, 0.05);
                border-left: 3px solid var(--vscode-testing-iconPassed);
            }

            .execution-step.error {
                background-color: rgba(255, 0, 0, 0.05);
                border-left: 3px solid var(--vscode-errorForeground);
            }

            .step-header {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .step-icon {
                font-size: 14px;
            }

            .step-title {
                flex: 1;
                font-weight: 500;
                color: var(--vscode-foreground);
            }

            .step-time {
                font-size: 11px;
                color: var(--vscode-descriptionForeground);
                opacity: 0.7;
            }

            .step-content {
                margin-top: 8px;
                padding: 12px;
                background-color: var(--vscode-editor-background);
                border: 1px solid var(--vscode-panel-border);
                border-radius: 4px;
            }

            .step-content pre {
                margin: 0;
                white-space: pre-wrap;
                word-wrap: break-word;
                font-family: var(--vscode-editor-font-family);
                font-size: 13px;
                line-height: 1.4;
            }

            .input-container {
                padding: 16px;
                background-color: var(--vscode-editorWidget-background);
                border-top: 1px solid var(--vscode-panel-border);
                flex-shrink: 0;
            }

            #messageInput {
                width: 100%;
                padding: 12px;
                border: 1px solid var(--vscode-input-border);
                border-radius: 4px;
                background-color: var(--vscode-input-background);
                color: var(--vscode-input-foreground);
                font-family: var(--vscode-font-family);
                font-size: 13px;
                resize: vertical;
                margin-bottom: 8px;
            }

            #messageInput:focus {
                outline: none;
                border-color: var(--vscode-focusBorder);
            }

            #sendBtn {
                width: 100%;
                padding: 10px;
                background: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-weight: 500;
                transition: background-color 0.2s ease;
            }

            #sendBtn:hover:not(:disabled) {
                background: var(--vscode-button-hoverBackground);
            }

            #sendBtn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }

            /* Chat UI Styles */
            .chat-container {
                flex: 1;
                overflow-y: auto;
                padding: 16px;
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            .chat-message {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .chat-message.user {
                align-items: flex-end;
            }

            .chat-message.assistant {
                align-items: flex-start;
            }

            .chat-message.system {
                align-items: center;
            }

            .message-header {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 12px;
                color: var(--vscode-descriptionForeground);
            }

            .message-sender {
                font-weight: 500;
            }

            .message-time {
                opacity: 0.7;
            }

            .message-content {
                max-width: 80%;
                display: flex;
                flex-direction: column;
                gap: 10px;
            }

            .chat-message.user .message-content {
                align-items: flex-end;
            }

            .chat-message.assistant .message-content {
                align-items: flex-start;
            }

            .chat-message.system .message-content {
                align-items: center;
                max-width: 100%;
            }

            .message-text {
                background-color: var(--vscode-textCodeBlock-background);
                border: 1px solid var(--vscode-panel-border);
                border-radius: 12px;
                padding: 12px 16px;
                word-wrap: break-word;
                line-height: 1.4;
            }

            .chat-message.user .message-text {
                background-color: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
            }

            .chat-message.system .message-text {
                background-color: var(--vscode-badge-background);
                color: var(--vscode-badge-foreground);
                text-align: center;
                font-style: italic;
            }

            /* Technical Elements */
            .technical-element {
                border: 1px solid var(--vscode-panel-border);
                border-radius: 6px;
                background-color: var(--vscode-editor-background);
                overflow: hidden;
                margin-top: 5px;
                max-width: 100%;
            }

            .technical-element.terminal {
                border-color: var(--vscode-terminal-border);
                background-color: var(--vscode-terminal-background);
            }

            .element-header {
                background-color: var(--vscode-sideBar-background);
                padding: 8px 12px;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 8px;
                border-bottom: 1px solid var(--vscode-panel-border);
                font-size: 13px;
            }

            .element-header:hover {
                background-color: var(--vscode-list-hoverBackground);
            }

            .technical-element.terminal .element-header {
                background-color: var(--vscode-terminal-background);
                color: var(--vscode-terminal-foreground);
            }

            .element-icon {
                font-size: 14px;
            }

            .element-title {
                font-weight: 500;
                color: var(--vscode-foreground);
            }

            .element-preview {
                flex: 1;
                color: var(--vscode-descriptionForeground);
                font-family: var(--vscode-editor-font-family);
                font-size: 12px;
                margin-left: 8px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .element-content {
                max-height: 400px;
                overflow: hidden;
                transition: max-height 0.3s ease;
            }

            .element-content.collapsed {
                max-height: 0;
            }

            .element-code {
                background-color: var(--vscode-textCodeBlock-background);
                color: var(--vscode-editor-foreground);
                font-family: var(--vscode-editor-font-family);
                font-size: var(--vscode-editor-font-size);
                padding: 12px;
                margin: 0;
                white-space: pre-wrap;
                word-wrap: break-word;
                overflow-y: auto;
                max-height: 350px;
            }

            .technical-element.terminal .element-code {
                background-color: var(--vscode-terminal-background);
                color: var(--vscode-terminal-foreground);
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 12px;
                line-height: 1.3;
                max-height: 350px;
                overflow-y: auto;
                border: none;
            }

            /* Terminal output styling */
            .terminal-output {
                background-color: var(--vscode-terminal-background) !important;
                color: var(--vscode-terminal-foreground) !important;
                font-family: 'Consolas', 'Courier New', monospace !important;
                font-size: 12px !important;
                line-height: 1.3 !important;
                white-space: normal !important;
                word-wrap: break-word !important;
            }

            .terminal-line {
                margin: 2px 0;
                padding: 1px 0;
                display: block;
                word-wrap: break-word;
            }

            .terminal-timestamp {
                color: var(--vscode-descriptionForeground);
                font-size: 11px;
                opacity: 0.8;
            }

            .terminal-type {
                font-weight: 600;
                margin-right: 4px;
            }

            .terminal-content {
                word-wrap: break-word;
            }

            .terminal-line.stdout .terminal-type {
                color: var(--vscode-charts-green);
            }

            .terminal-line.stderr .terminal-type {
                color: var(--vscode-errorForeground);
            }

            .terminal-line.progress .terminal-type {
                color: var(--vscode-progressBar-foreground);
            }

            .terminal-line.info .terminal-type {
                color: var(--vscode-charts-blue);
            }

            .terminal-line.error .terminal-type {
                color: var(--vscode-errorForeground);
            }

            .terminal-line.stdout {
                color: var(--vscode-terminal-foreground);
            }

            .terminal-line.stderr {
                color: var(--vscode-errorForeground);
            }

            .terminal-line.progress {
                color: var(--vscode-progressBar-foreground);
                font-weight: 500;
            }

            .terminal-line.info {
                color: var(--vscode-charts-blue);
            }

            .terminal-line.error {
                color: var(--vscode-errorForeground);
                font-weight: 500;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
        `;
    }

    /**
     * Get JavaScript code
     */
    private getScript(): string {
        return `
            const vscode = acquireVsCodeApi();

            function sendMessage() {
                const input = document.getElementById('messageInput');
                const text = input.value.trim();
                if (text) {
                    vscode.postMessage({
                        command: 'sendMessage',
                        text: text
                    });
                    input.value = '';
                }
            }

            function toggleElement(messageId, elementId) {
                vscode.postMessage({
                    command: 'toggleElement',
                    messageId: messageId,
                    elementId: elementId
                });
            }

            function clearHistory() {
                vscode.postMessage({
                    command: 'clearHistory'
                });
            }

            // Handle Enter key in textarea
            document.getElementById('messageInput').addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Auto-focus input
            document.getElementById('messageInput').focus();
        `;
    }
}
