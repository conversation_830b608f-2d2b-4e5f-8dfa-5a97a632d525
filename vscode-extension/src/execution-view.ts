import * as vscode from 'vscode';
import { BridgeClient } from './bridge-client';

interface ChatMessage {
    id: string;
    type: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: string;
    elements?: TechnicalElement[];
}

interface TechnicalElement {
    id: string;
    type: 'command' | 'code' | 'file_operation' | 'output';
    title: string;
    preview?: string;
    content: string;
    language?: string;
    collapsed: boolean;
    status?: 'pending' | 'running' | 'completed' | 'error';
}

interface ExecutionStep {
    id: string;
    title: string;
    status: 'pending' | 'running' | 'completed' | 'error';
    content?: string;
    timestamp: string;
}

interface ExecutionBlock {
    id: string;
    title: string;
    status: 'pending' | 'running' | 'completed' | 'error';
    steps: ExecutionStep[];
    collapsed: boolean;
    timestamp: string;
}

export class ExecutionView {
    private panel: vscode.WebviewPanel | undefined;
    private bridgeClient: BridgeClient;
    private context: vscode.ExtensionContext;
    private chatMessages: ChatMessage[] = [];
    private executionBlocks: ExecutionBlock[] = [];
    private currentChatSessionId: string | null = null;
    private isProcessing: boolean = false;
    private useNewChatUI: boolean = true; // Toggle for new UI

    constructor(context: vscode.ExtensionContext, bridgeClient: BridgeClient) {
        this.context = context;
        this.bridgeClient = bridgeClient;
    }

    /**
     * Show the execution view
     */
    public show() {
        if (this.panel) {
            this.panel.reveal(vscode.ViewColumn.Beside);
            return;
        }

        this.panel = vscode.window.createWebviewPanel(
            'aiCodingAgentExecution',
            'AI Coding Agent',
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [this.context.extensionUri]
            }
        );

        this.panel.webview.html = this.getHtmlForWebview();

        // Handle messages from the webview
        this.panel.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.command) {
                    case 'sendMessage':
                        await this.sendMessage(message.text);
                        break;
                    case 'toggleBlock':
                        this.toggleBlock(message.blockId);
                        break;
                    case 'toggleElement':
                        this.toggleTechnicalElement(message.messageId, message.elementId);
                        break;
                    case 'clearHistory':
                        this.clearHistory();
                        break;
                    case 'toggleUI':
                        this.toggleUIMode();
                        break;
                }
            },
            undefined,
            this.context.subscriptions
        );

        this.panel.onDidDispose(
            () => {
                this.panel = undefined;
            },
            null,
            this.context.subscriptions
        );

        // Add initial welcome message
        this.addWelcomeMessage();
    }

    /**
     * Send a message to the AI agent
     */
    private async sendMessage(text: string) {
        if (this.isProcessing) {
            vscode.window.showWarningMessage('Please wait for the current request to complete.');
            return;
        }

        this.isProcessing = true;

        try {
            if (this.useNewChatUI) {
                await this.sendMessageNewUI(text);
            } else {
                await this.sendMessageOldUI(text);
            }
        } finally {
            this.isProcessing = false;
            this.updateWebview();
        }
    }

    /**
     * Send message using new chat UI with SWE-Agent integration
     */
    private async sendMessageNewUI(text: string) {
        // Add user message
        this.addChatMessage('user', text);

        // Create or reuse SWE-Agent session
        if (!this.currentChatSessionId) {
            this.addChatMessage('system', 'Creating SWE-Agent session...');

            try {
                // Use SWE-Agent continuous mode for better task completion
                const response = await this.bridgeClient.runAgentContinuous(
                    text,
                    this.getWorkspaceContext().workspace_path || '/u/Arun Dev/Python Projects/AI-Coding-Agent',
                    'claude-3-5-sonnet-20241022',
                    true, // continuous mode
                    false // parallel tasks
                );

                if (response.status === 'success' && response.session_id) {
                    this.currentChatSessionId = response.session_id;
                    this.updateLastSystemMessage('SWE-Agent session created and started');

                    // Add progress tracking message
                    const progressMessageId = this.addChatMessage('assistant', '🔄 SWE-Agent is analyzing your request...');

                    // Monitor session progress
                    this.monitorSessionProgress(progressMessageId);
                } else {
                    this.updateLastSystemMessage(`Error creating SWE-Agent session: ${response.error || 'Unknown error'}`);
                    return;
                }
            } catch (error: any) {
                this.updateLastSystemMessage(`Error creating SWE-Agent session: ${error.message}`);
                return;
            }
        } else {
            // Session exists, send additional message
            this.addChatMessage('system', 'Sending additional instruction to SWE-Agent...');

            try {
                // For existing sessions, we can send follow-up instructions
                const progressMessageId = this.addChatMessage('assistant', '🔄 Processing additional instruction...');

                // Get current session status and trajectory
                const sessionDetails = await this.bridgeClient.getSessionDetails(this.currentChatSessionId);

                if (sessionDetails.status === 'completed' || sessionDetails.status === 'failed') {
                    // Session ended, create new one
                    this.currentChatSessionId = null;
                    this.updateChatMessage(progressMessageId, '🔄 Previous session ended, creating new session...');
                    await this.sendMessageNewUI(text); // Recursive call to create new session
                    return;
                } else {
                    // Session is still active, show current progress
                    this.updateChatMessage(progressMessageId, `🔄 SWE-Agent is working... Status: ${sessionDetails.status}`);
                    this.monitorSessionProgress(progressMessageId);
                }
            } catch (error: any) {
                const errorMessageId = this.addChatMessage('assistant', `❌ Error: ${error.message}`);
            }
        }
    }

    /**
     * Send message using old execution block UI
     */
    private async sendMessageOldUI(text: string) {
        // Create execution block
        const blockId = this.createExecutionBlock(text);

        // Add steps
        this.addStep(blockId, 'Analyzing your request...', 'running');

        // Create or reuse chat session
        if (!this.currentChatSessionId) {
            this.addStep(blockId, 'Creating chat session...', 'running');

            try {
                const chatSession = await this.bridgeClient.createChatSession(
                    'VS Code Execution Session',
                    this.getWorkspaceContext()
                );
                this.currentChatSessionId = chatSession.session_id;
                this.updateStepStatus(blockId, 1, 'completed');
            } catch (error: any) {
                this.updateStepStatus(blockId, 1, 'error', error.message);
                return;
            }
        } else {
            this.updateStepStatus(blockId, 0, 'completed');
        }

        // Send message
        this.addStep(blockId, 'Generating AI response...', 'running');

        try {
            const response = await this.bridgeClient.sendChatMessage(
                this.currentChatSessionId,
                text,
                this.getWorkspaceContext()
            );

            if (response.status === 'success' && response.response) {
                this.updateStepStatus(blockId, -1, 'completed');
                this.addStep(blockId, 'Response received', 'completed', response.response);
                this.completeExecutionBlock(blockId);
            } else {
                this.updateStepStatus(blockId, -1, 'error', response.error || 'Failed to get response');
            }
        } catch (error: any) {
            this.updateStepStatus(blockId, -1, 'error', `Error: ${error.message}`);
        }
    }

    /**
     * Add a chat message
     */
    private addChatMessage(type: 'user' | 'assistant' | 'system', content: string): string {
        const messageId = `msg_${Date.now()}`;
        const message: ChatMessage = {
            id: messageId,
            type: type,
            content: content,
            timestamp: new Date().toLocaleTimeString(),
            elements: []
        };

        this.chatMessages.push(message);
        this.updateWebview();
        return messageId;
    }

    /**
     * Update a chat message
     */
    private updateChatMessage(messageId: string, content: string) {
        const message = this.chatMessages.find(m => m.id === messageId);
        if (message) {
            message.content = content;
            this.updateWebview();
        }
    }

    /**
     * Update last system message
     */
    private updateLastSystemMessage(content: string) {
        const lastSystemMessage = [...this.chatMessages].reverse().find(m => m.type === 'system');
        if (lastSystemMessage) {
            lastSystemMessage.content = content;
            this.updateWebview();
        }
    }

    /**
     * Parse response and add technical elements
     */
    private parseAndAddTechnicalElements(messageId: string, content: string) {
        const message = this.chatMessages.find(m => m.id === messageId);
        if (!message) return;

        // Parse for code blocks
        const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
        let match;
        while ((match = codeBlockRegex.exec(content)) !== null) {
            const language = match[1] || 'text';
            const code = match[2];
            const preview = code.split('\n').slice(0, 3).join('\n') + (code.split('\n').length > 3 ? '\n...' : '');

            this.addTechnicalElement(messageId, 'code', `Code (${language})`, preview, code, language);
        }

        // Parse for terminal commands
        const commandRegex = /(?:^|\n)\$\s+(.+)/g;
        while ((match = commandRegex.exec(content)) !== null) {
            const command = match[1];
            this.addTechnicalElement(messageId, 'command', 'Terminal Command', command, command);
        }

        // Parse for file operations
        const fileOpRegex = /(?:created|modified|deleted|updated)\s+(?:file\s+)?[`']?([^\s`']+)[`']?/gi;
        while ((match = fileOpRegex.exec(content)) !== null) {
            const filePath = match[1];
            this.addTechnicalElement(messageId, 'file_operation', 'File Operation', filePath, `File: ${filePath}`);
        }
    }

    /**
     * Add technical element to a message
     */
    private addTechnicalElement(messageId: string, type: TechnicalElement['type'], title: string, preview: string, content: string, language?: string) {
        const message = this.chatMessages.find(m => m.id === messageId);
        if (!message) return;

        const element: TechnicalElement = {
            id: `elem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: type,
            title: title,
            preview: preview,
            content: content,
            language: language,
            collapsed: true
        };

        message.elements = message.elements || [];
        message.elements.push(element);
        this.updateWebview();
    }

    /**
     * Toggle technical element
     */
    private toggleTechnicalElement(messageId: string, elementId: string) {
        const message = this.chatMessages.find(m => m.id === messageId);
        if (!message || !message.elements) return;

        const element = message.elements.find(e => e.id === elementId);
        if (element) {
            element.collapsed = !element.collapsed;
            this.updateWebview();
        }
    }

    /**
     * Toggle UI mode
     */
    private toggleUIMode() {
        this.useNewChatUI = !this.useNewChatUI;
        this.updateWebview();
    }

    /**
     * Create a new execution block
     */
    private createExecutionBlock(title: string): string {
        const blockId = `block_${Date.now()}`;
        const block: ExecutionBlock = {
            id: blockId,
            title: title,
            status: 'running',
            steps: [],
            collapsed: false,
            timestamp: new Date().toLocaleTimeString()
        };

        this.executionBlocks.push(block);
        this.updateWebview();
        return blockId;
    }

    /**
     * Add a step to an execution block
     */
    private addStep(blockId: string, title: string, status: 'pending' | 'running' | 'completed' | 'error', content?: string) {
        const block = this.executionBlocks.find(b => b.id === blockId);
        if (!block) return;

        const step: ExecutionStep = {
            id: `step_${Date.now()}`,
            title: title,
            status: status,
            content: content,
            timestamp: new Date().toLocaleTimeString()
        };

        block.steps.push(step);
        this.updateWebview();
    }

    /**
     * Update step status
     */
    private updateStepStatus(blockId: string, stepIndex: number, status: 'pending' | 'running' | 'completed' | 'error', content?: string) {
        const block = this.executionBlocks.find(b => b.id === blockId);
        if (!block) return;

        // Handle negative index (last step)
        const index = stepIndex < 0 ? block.steps.length + stepIndex : stepIndex;
        if (index >= 0 && index < block.steps.length) {
            block.steps[index].status = status;
            if (content) {
                block.steps[index].content = content;
            }
            this.updateWebview();
        }
    }

    /**
     * Complete an execution block
     */
    private completeExecutionBlock(blockId: string) {
        const block = this.executionBlocks.find(b => b.id === blockId);
        if (!block) return;

        block.status = 'completed';
        block.collapsed = true; // Auto-collapse completed blocks
        this.updateWebview();
    }

    /**
     * Toggle block collapsed state
     */
    private toggleBlock(blockId: string) {
        const block = this.executionBlocks.find(b => b.id === blockId);
        if (block) {
            block.collapsed = !block.collapsed;
            this.updateWebview();
        }
    }

    /**
     * Clear execution history
     */
    private clearHistory() {
        this.chatMessages = [];
        this.executionBlocks = [];
        this.currentChatSessionId = null;
        this.addWelcomeMessage();
        this.updateWebview();
    }

    /**
     * Add welcome message
     */
    private addWelcomeMessage() {
        if (this.useNewChatUI) {
            this.addChatMessage('system', '🤖 Welcome to AI Coding Agent! I can help you analyze code, fix bugs, implement features, and more. Just type your request below!');
        } else {
            const blockId = this.createExecutionBlock('Welcome to AI Coding Agent');
            this.addStep(blockId, 'Ready to assist with your coding tasks', 'completed',
                'I can help you analyze code, fix bugs, implement features, and more. Just type your request below!');
            this.completeExecutionBlock(blockId);
        }
    }

    /**
     * Get workspace context
     */
    private getWorkspaceContext(): any {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) return {};

        const activeEditor = vscode.window.activeTextEditor;
        const context: any = {
            project_root: workspaceFolders[0].uri.fsPath,
            workspace_name: workspaceFolders[0].name,
            capabilities: {
                file_operations: true,
                terminal_execution: true,
                codebase_analysis: true,
                project_navigation: true
            }
        };

        if (activeEditor) {
            context.current_file = {
                path: activeEditor.document.uri.fsPath,
                relative_path: vscode.workspace.asRelativePath(activeEditor.document.uri),
                language: activeEditor.document.languageId,
                is_dirty: activeEditor.document.isDirty,
                line_count: activeEditor.document.lineCount
            };
        }

        return context;
    }

    /**
     * Update the webview content
     */
    private updateWebview() {
        if (this.panel) {
            this.panel.webview.html = this.getHtmlForWebview();
        }
    }

    /**
     * Get HTML for the webview
     */
    private getHtmlForWebview(): string {
        const contentHtml = this.useNewChatUI ? this.renderChatUI() : this.renderExecutionUI();

        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>AI Coding Agent</title>
                <style>
                    ${this.getStyles()}
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>🤖 AI Coding Agent</h2>
                    <div class="session-info">
                        ${this.currentChatSessionId ? `Session: ${this.currentChatSessionId.substring(0, 8)}...` : 'No active session'}
                        <button onclick="toggleUI()" class="toggle-btn">${this.useNewChatUI ? '📋 Old UI' : '💬 Chat UI'}</button>
                        <button onclick="clearHistory()" class="clear-btn">🗑️ Clear</button>
                    </div>
                </div>

                ${contentHtml}

                <div class="input-container">
                    <textarea id="messageInput" placeholder="${this.useNewChatUI ? 'Type your message...' : 'Ask me anything about your code...'}" rows="3"></textarea>
                    <button onclick="sendMessage()" id="sendBtn" ${this.isProcessing ? 'disabled' : ''}>
                        ${this.isProcessing ? '⏳ Processing...' : '🚀 Send'}
                    </button>
                </div>

                <script>
                    ${this.getScript()}
                </script>
            </body>
            </html>
        `;
    }

    /**
     * Render chat UI
     */
    private renderChatUI(): string {
        const messagesHtml = this.chatMessages.map(message => this.renderChatMessage(message)).join('');

        return `
            <div class="chat-container">
                ${messagesHtml}
            </div>
        `;
    }

    /**
     * Render execution UI
     */
    private renderExecutionUI(): string {
        const blocksHtml = this.executionBlocks.map(block => this.renderExecutionBlock(block)).join('');

        return `
            <div class="execution-container">
                ${blocksHtml}
            </div>
        `;
    }

    /**
     * Render a chat message
     */
    private renderChatMessage(message: ChatMessage): string {
        const elementsHtml = message.elements ? message.elements.map(element => this.renderTechnicalElement(message.id, element)).join('') : '';

        return `
            <div class="chat-message ${message.type}" data-message-id="${message.id}">
                <div class="message-header">
                    <span class="message-sender">${this.getSenderIcon(message.type)} ${this.getSenderName(message.type)}</span>
                    <span class="message-time">${message.timestamp}</span>
                </div>
                <div class="message-content">
                    <div class="message-text">${this.formatMessageContent(message.content)}</div>
                    ${elementsHtml}
                </div>
            </div>
        `;
    }

    /**
     * Render technical element
     */
    private renderTechnicalElement(messageId: string, element: TechnicalElement): string {
        const toggleIcon = element.collapsed ? '▶' : '▼';
        const typeIcon = this.getTechnicalElementIcon(element.type);

        return `
            <div class="technical-element ${element.type}" data-element-id="${element.id}">
                <div class="element-header" onclick="toggleElement('${messageId}', '${element.id}')">
                    <span class="toggle-icon">${toggleIcon}</span>
                    <span class="element-icon">${typeIcon}</span>
                    <span class="element-title">${this.escapeHtml(element.title)}</span>
                    ${element.preview ? `<span class="element-preview">${this.escapeHtml(element.preview)}</span>` : ''}
                </div>
                <div class="element-content ${element.collapsed ? 'collapsed' : ''}">
                    <pre class="element-code ${element.language || ''}">${this.escapeHtml(element.content)}</pre>
                </div>
            </div>
        `;
    }

    /**
     * Get sender icon
     */
    private getSenderIcon(type: string): string {
        switch (type) {
            case 'user': return '👤';
            case 'assistant': return '🤖';
            case 'system': return '⚙️';
            default: return '💬';
        }
    }

    /**
     * Get sender name
     */
    private getSenderName(type: string): string {
        switch (type) {
            case 'user': return 'You';
            case 'assistant': return 'AI Assistant';
            case 'system': return 'System';
            default: return 'Unknown';
        }
    }

    /**
     * Get technical element icon
     */
    private getTechnicalElementIcon(type: string): string {
        switch (type) {
            case 'command': return '💻';
            case 'code': return '📝';
            case 'file_operation': return '📁';
            case 'output': return '📄';
            default: return '🔧';
        }
    }

    /**
     * Format message content
     */
    private formatMessageContent(content: string): string {
        // Remove code blocks and commands that will be shown as technical elements
        let formatted = content
            .replace(/```[\w]*\n[\s\S]*?```/g, '') // Remove code blocks
            .replace(/(?:^|\n)\$\s+.+/g, '') // Remove terminal commands
            .trim();

        // Convert markdown-style formatting
        formatted = formatted
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
            .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic
            .replace(/`(.*?)`/g, '<code>$1</code>') // Inline code
            .replace(/\n/g, '<br>'); // Line breaks

        return formatted || content; // Fallback to original if empty
    }

    /**
     * Render an execution block
     */
    private renderExecutionBlock(block: ExecutionBlock): string {
        const stepsHtml = block.steps.map(step => this.renderStep(step)).join('');
        const toggleIcon = block.collapsed ? '▶' : '▼';

        return `
            <div class="execution-block ${block.status}" data-block-id="${block.id}">
                <div class="block-header" onclick="toggleBlock('${block.id}')">
                    <span class="toggle-icon">${toggleIcon}</span>
                    <span class="block-title">${this.escapeHtml(block.title)}</span>
                    <span class="block-status ${block.status}"></span>
                    <span class="block-time">${block.timestamp}</span>
                </div>
                <div class="block-content ${block.collapsed ? 'collapsed' : ''}">
                    ${stepsHtml}
                </div>
            </div>
        `;
    }

    /**
     * Render a step
     */
    private renderStep(step: ExecutionStep): string {
        let statusIcon = '';
        switch (step.status) {
            case 'pending': statusIcon = '⏳'; break;
            case 'running': statusIcon = '🔄'; break;
            case 'completed': statusIcon = '✅'; break;
            case 'error': statusIcon = '❌'; break;
        }

        const contentHtml = step.content ? `
            <div class="step-content">
                <pre>${this.escapeHtml(step.content)}</pre>
            </div>
        ` : '';

        return `
            <div class="execution-step ${step.status}">
                <div class="step-header">
                    <span class="step-icon">${statusIcon}</span>
                    <span class="step-title">${this.escapeHtml(step.title)}</span>
                    <span class="step-time">${step.timestamp}</span>
                </div>
                ${contentHtml}
            </div>
        `;
    }

    /**
     * Escape HTML
     */
    private escapeHtml(text: string): string {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    /**
     * Get CSS styles
     */
    private getStyles(): string {
        return `
            body {
                font-family: var(--vscode-font-family);
                font-size: var(--vscode-font-size);
                color: var(--vscode-foreground);
                background-color: var(--vscode-editor-background);
                margin: 0;
                padding: 0;
                height: 100vh;
                display: flex;
                flex-direction: column;
            }

            .header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 16px;
                background-color: var(--vscode-titleBar-activeBackground);
                border-bottom: 1px solid var(--vscode-panel-border);
                flex-shrink: 0;
            }

            .header h2 {
                margin: 0;
                color: var(--vscode-titleBar-activeForeground);
                font-size: 16px;
                font-weight: 600;
            }

            .session-info {
                display: flex;
                align-items: center;
                gap: 12px;
                font-size: 12px;
                color: var(--vscode-descriptionForeground);
            }

            .clear-btn {
                background: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border: none;
                padding: 4px 8px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 11px;
            }

            .clear-btn:hover {
                background: var(--vscode-button-hoverBackground);
            }

            .execution-container {
                flex: 1;
                overflow-y: auto;
                padding: 16px;
            }

            .execution-block {
                margin-bottom: 16px;
                border: 1px solid var(--vscode-panel-border);
                border-radius: 8px;
                background-color: var(--vscode-editorWidget-background);
                overflow: hidden;
            }

            .execution-block.completed {
                border-color: var(--vscode-testing-iconPassed);
            }

            .execution-block.error {
                border-color: var(--vscode-errorForeground);
            }

            .execution-block.running {
                border-color: var(--vscode-progressBar-foreground);
            }

            .block-header {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                background: linear-gradient(135deg, var(--vscode-button-background), var(--vscode-button-hoverBackground));
                color: var(--vscode-button-foreground);
                cursor: pointer;
                transition: background-color 0.2s ease;
            }

            .block-header:hover {
                background: var(--vscode-button-hoverBackground);
            }

            .toggle-icon {
                margin-right: 8px;
                font-size: 12px;
                transition: transform 0.2s ease;
            }

            .block-title {
                flex: 1;
                font-weight: 500;
                font-size: 14px;
            }

            .block-status {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                margin-right: 8px;
            }

            .block-status.pending {
                background-color: var(--vscode-charts-yellow);
            }

            .block-status.running {
                background-color: var(--vscode-progressBar-foreground);
                animation: pulse 2s infinite;
            }

            .block-status.completed {
                background-color: var(--vscode-testing-iconPassed);
            }

            .block-status.error {
                background-color: var(--vscode-errorForeground);
            }

            .block-time {
                font-size: 11px;
                opacity: 0.7;
            }

            .block-content {
                max-height: 1000px;
                overflow: hidden;
                transition: max-height 0.3s ease;
            }

            .block-content.collapsed {
                max-height: 0;
            }

            .execution-step {
                padding: 12px 16px;
                border-bottom: 1px solid var(--vscode-panel-border);
            }

            .execution-step:last-child {
                border-bottom: none;
            }

            .execution-step.running {
                background-color: rgba(255, 165, 0, 0.1);
                border-left: 3px solid var(--vscode-progressBar-foreground);
            }

            .execution-step.completed {
                background-color: rgba(0, 255, 0, 0.05);
                border-left: 3px solid var(--vscode-testing-iconPassed);
            }

            .execution-step.error {
                background-color: rgba(255, 0, 0, 0.05);
                border-left: 3px solid var(--vscode-errorForeground);
            }

            .step-header {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .step-icon {
                font-size: 14px;
            }

            .step-title {
                flex: 1;
                font-weight: 500;
                color: var(--vscode-foreground);
            }

            .step-time {
                font-size: 11px;
                color: var(--vscode-descriptionForeground);
                opacity: 0.7;
            }

            .step-content {
                margin-top: 8px;
                padding: 12px;
                background-color: var(--vscode-editor-background);
                border: 1px solid var(--vscode-panel-border);
                border-radius: 4px;
            }

            .step-content pre {
                margin: 0;
                white-space: pre-wrap;
                word-wrap: break-word;
                font-family: var(--vscode-editor-font-family);
                font-size: 13px;
                line-height: 1.4;
            }

            .input-container {
                padding: 16px;
                background-color: var(--vscode-editorWidget-background);
                border-top: 1px solid var(--vscode-panel-border);
                flex-shrink: 0;
            }

            #messageInput {
                width: 100%;
                padding: 12px;
                border: 1px solid var(--vscode-input-border);
                border-radius: 4px;
                background-color: var(--vscode-input-background);
                color: var(--vscode-input-foreground);
                font-family: var(--vscode-font-family);
                font-size: 13px;
                resize: vertical;
                margin-bottom: 8px;
            }

            #messageInput:focus {
                outline: none;
                border-color: var(--vscode-focusBorder);
            }

            #sendBtn {
                width: 100%;
                padding: 10px;
                background: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-weight: 500;
                transition: background-color 0.2s ease;
            }

            #sendBtn:hover:not(:disabled) {
                background: var(--vscode-button-hoverBackground);
            }

            #sendBtn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }

            /* Chat UI Styles */
            .chat-container {
                flex: 1;
                overflow-y: auto;
                padding: 16px;
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            .chat-message {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .chat-message.user {
                align-items: flex-end;
            }

            .chat-message.assistant {
                align-items: flex-start;
            }

            .chat-message.system {
                align-items: center;
            }

            .message-header {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 12px;
                color: var(--vscode-descriptionForeground);
            }

            .message-sender {
                font-weight: 500;
            }

            .message-time {
                opacity: 0.7;
            }

            .message-content {
                max-width: 80%;
                display: flex;
                flex-direction: column;
                gap: 10px;
            }

            .chat-message.user .message-content {
                align-items: flex-end;
            }

            .chat-message.assistant .message-content {
                align-items: flex-start;
            }

            .chat-message.system .message-content {
                align-items: center;
                max-width: 100%;
            }

            .message-text {
                background-color: var(--vscode-textCodeBlock-background);
                border: 1px solid var(--vscode-panel-border);
                border-radius: 12px;
                padding: 12px 16px;
                word-wrap: break-word;
                line-height: 1.4;
            }

            .chat-message.user .message-text {
                background-color: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
            }

            .chat-message.system .message-text {
                background-color: var(--vscode-badge-background);
                color: var(--vscode-badge-foreground);
                text-align: center;
                font-style: italic;
            }

            /* Technical Elements */
            .technical-element {
                border: 1px solid var(--vscode-panel-border);
                border-radius: 6px;
                background-color: var(--vscode-editor-background);
                overflow: hidden;
                margin-top: 5px;
                max-width: 100%;
            }

            .element-header {
                background-color: var(--vscode-sideBar-background);
                padding: 8px 12px;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 8px;
                border-bottom: 1px solid var(--vscode-panel-border);
                font-size: 13px;
            }

            .element-header:hover {
                background-color: var(--vscode-list-hoverBackground);
            }

            .element-icon {
                font-size: 14px;
            }

            .element-title {
                font-weight: 500;
                color: var(--vscode-foreground);
            }

            .element-preview {
                flex: 1;
                color: var(--vscode-descriptionForeground);
                font-family: var(--vscode-editor-font-family);
                font-size: 12px;
                margin-left: 8px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .element-content {
                max-height: 300px;
                overflow: hidden;
                transition: max-height 0.3s ease;
            }

            .element-content.collapsed {
                max-height: 0;
            }

            .element-code {
                background-color: var(--vscode-textCodeBlock-background);
                color: var(--vscode-editor-foreground);
                font-family: var(--vscode-editor-font-family);
                font-size: var(--vscode-editor-font-size);
                padding: 12px;
                margin: 0;
                white-space: pre-wrap;
                word-wrap: break-word;
                overflow-y: auto;
                max-height: 250px;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
        `;
    }

    /**
     * Get JavaScript code
     */
    private getScript(): string {
        return `
            const vscode = acquireVsCodeApi();

            function sendMessage() {
                const input = document.getElementById('messageInput');
                const text = input.value.trim();
                if (text) {
                    vscode.postMessage({
                        command: 'sendMessage',
                        text: text
                    });
                    input.value = '';
                }
            }

            function toggleBlock(blockId) {
                vscode.postMessage({
                    command: 'toggleBlock',
                    blockId: blockId
                });
            }

            function toggleElement(messageId, elementId) {
                vscode.postMessage({
                    command: 'toggleElement',
                    messageId: messageId,
                    elementId: elementId
                });
            }

            function toggleUI() {
                vscode.postMessage({
                    command: 'toggleUI'
                });
            }

            function clearHistory() {
                vscode.postMessage({
                    command: 'clearHistory'
                });
            }

            // Handle Enter key in textarea
            document.getElementById('messageInput').addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Auto-focus input
            document.getElementById('messageInput').focus();
        `;
    }
}
