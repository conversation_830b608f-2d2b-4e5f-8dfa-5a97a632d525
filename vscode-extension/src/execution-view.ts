import * as vscode from 'vscode';
import { BridgeClient } from './bridge-client';

interface ExecutionStep {
    id: string;
    title: string;
    status: 'pending' | 'running' | 'completed' | 'error';
    content?: string;
    timestamp: string;
}

interface ExecutionBlock {
    id: string;
    title: string;
    status: 'pending' | 'running' | 'completed' | 'error';
    steps: ExecutionStep[];
    collapsed: boolean;
    timestamp: string;
}

export class ExecutionView {
    private panel: vscode.WebviewPanel | undefined;
    private bridgeClient: BridgeClient;
    private context: vscode.ExtensionContext;
    private executionBlocks: ExecutionBlock[] = [];
    private currentChatSessionId: string | null = null;
    private isProcessing: boolean = false;

    constructor(context: vscode.ExtensionContext, bridgeClient: BridgeClient) {
        this.context = context;
        this.bridgeClient = bridgeClient;
    }

    /**
     * Show the execution view
     */
    public show() {
        if (this.panel) {
            this.panel.reveal(vscode.ViewColumn.Beside);
            return;
        }

        this.panel = vscode.window.createWebviewPanel(
            'aiCodingAgentExecution',
            'AI Coding Agent',
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [this.context.extensionUri]
            }
        );

        this.panel.webview.html = this.getHtmlForWebview();

        // Handle messages from the webview
        this.panel.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.command) {
                    case 'sendMessage':
                        await this.sendMessage(message.text);
                        break;
                    case 'toggleBlock':
                        this.toggleBlock(message.blockId);
                        break;
                    case 'clearHistory':
                        this.clearHistory();
                        break;
                }
            },
            undefined,
            this.context.subscriptions
        );

        this.panel.onDidDispose(
            () => {
                this.panel = undefined;
            },
            null,
            this.context.subscriptions
        );

        // Add initial welcome message
        this.addWelcomeMessage();
    }

    /**
     * Send a message to the AI agent
     */
    private async sendMessage(text: string) {
        if (this.isProcessing) {
            vscode.window.showWarningMessage('Please wait for the current request to complete.');
            return;
        }

        this.isProcessing = true;

        try {
            // Create execution block
            const blockId = this.createExecutionBlock(text);
            
            // Add steps
            this.addStep(blockId, 'Analyzing your request...', 'running');

            // Create or reuse chat session
            if (!this.currentChatSessionId) {
                this.addStep(blockId, 'Creating chat session...', 'running');
                
                try {
                    const chatSession = await this.bridgeClient.createChatSession(
                        'VS Code Execution Session',
                        this.getWorkspaceContext()
                    );
                    this.currentChatSessionId = chatSession.session_id;
                    this.updateStepStatus(blockId, 1, 'completed');
                } catch (error: any) {
                    this.updateStepStatus(blockId, 1, 'error', error.message);
                    return;
                }
            } else {
                this.updateStepStatus(blockId, 0, 'completed');
            }

            // Send message
            this.addStep(blockId, 'Generating AI response...', 'running');
            
            try {
                const response = await this.bridgeClient.sendChatMessage(
                    this.currentChatSessionId, 
                    text, 
                    this.getWorkspaceContext()
                );

                if (response.status === 'success' && response.response) {
                    this.updateStepStatus(blockId, -1, 'completed');
                    this.addStep(blockId, 'Response received', 'completed', response.response);
                    this.completeExecutionBlock(blockId);
                } else {
                    this.updateStepStatus(blockId, -1, 'error', response.error || 'Failed to get response');
                }
            } catch (error: any) {
                this.updateStepStatus(blockId, -1, 'error', `Error: ${error.message}`);
            }
        } finally {
            this.isProcessing = false;
            this.updateWebview();
        }
    }

    /**
     * Create a new execution block
     */
    private createExecutionBlock(title: string): string {
        const blockId = `block_${Date.now()}`;
        const block: ExecutionBlock = {
            id: blockId,
            title: title,
            status: 'running',
            steps: [],
            collapsed: false,
            timestamp: new Date().toLocaleTimeString()
        };

        this.executionBlocks.push(block);
        this.updateWebview();
        return blockId;
    }

    /**
     * Add a step to an execution block
     */
    private addStep(blockId: string, title: string, status: 'pending' | 'running' | 'completed' | 'error', content?: string) {
        const block = this.executionBlocks.find(b => b.id === blockId);
        if (!block) return;

        const step: ExecutionStep = {
            id: `step_${Date.now()}`,
            title: title,
            status: status,
            content: content,
            timestamp: new Date().toLocaleTimeString()
        };

        block.steps.push(step);
        this.updateWebview();
    }

    /**
     * Update step status
     */
    private updateStepStatus(blockId: string, stepIndex: number, status: 'pending' | 'running' | 'completed' | 'error', content?: string) {
        const block = this.executionBlocks.find(b => b.id === blockId);
        if (!block) return;

        // Handle negative index (last step)
        const index = stepIndex < 0 ? block.steps.length + stepIndex : stepIndex;
        if (index >= 0 && index < block.steps.length) {
            block.steps[index].status = status;
            if (content) {
                block.steps[index].content = content;
            }
            this.updateWebview();
        }
    }

    /**
     * Complete an execution block
     */
    private completeExecutionBlock(blockId: string) {
        const block = this.executionBlocks.find(b => b.id === blockId);
        if (!block) return;

        block.status = 'completed';
        block.collapsed = true; // Auto-collapse completed blocks
        this.updateWebview();
    }

    /**
     * Toggle block collapsed state
     */
    private toggleBlock(blockId: string) {
        const block = this.executionBlocks.find(b => b.id === blockId);
        if (block) {
            block.collapsed = !block.collapsed;
            this.updateWebview();
        }
    }

    /**
     * Clear execution history
     */
    private clearHistory() {
        this.executionBlocks = [];
        this.currentChatSessionId = null;
        this.addWelcomeMessage();
        this.updateWebview();
    }

    /**
     * Add welcome message
     */
    private addWelcomeMessage() {
        const blockId = this.createExecutionBlock('Welcome to AI Coding Agent');
        this.addStep(blockId, 'Ready to assist with your coding tasks', 'completed', 
            'I can help you analyze code, fix bugs, implement features, and more. Just type your request below!');
        this.completeExecutionBlock(blockId);
    }

    /**
     * Get workspace context
     */
    private getWorkspaceContext(): any {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) return {};

        const activeEditor = vscode.window.activeTextEditor;
        const context: any = {
            project_root: workspaceFolders[0].uri.fsPath,
            workspace_name: workspaceFolders[0].name,
            capabilities: {
                file_operations: true,
                terminal_execution: true,
                codebase_analysis: true,
                project_navigation: true
            }
        };

        if (activeEditor) {
            context.current_file = {
                path: activeEditor.document.uri.fsPath,
                relative_path: vscode.workspace.asRelativePath(activeEditor.document.uri),
                language: activeEditor.document.languageId,
                is_dirty: activeEditor.document.isDirty,
                line_count: activeEditor.document.lineCount
            };
        }

        return context;
    }

    /**
     * Update the webview content
     */
    private updateWebview() {
        if (this.panel) {
            this.panel.webview.html = this.getHtmlForWebview();
        }
    }

    /**
     * Get HTML for the webview
     */
    private getHtmlForWebview(): string {
        const blocksHtml = this.executionBlocks.map(block => this.renderExecutionBlock(block)).join('');

        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>AI Coding Agent</title>
                <style>
                    ${this.getStyles()}
                </style>
            </head>
            <body>
                <div class="header">
                    <h2>🤖 AI Coding Agent</h2>
                    <div class="session-info">
                        ${this.currentChatSessionId ? `Session: ${this.currentChatSessionId.substring(0, 8)}...` : 'No active session'}
                        <button onclick="clearHistory()" class="clear-btn">🗑️ Clear</button>
                    </div>
                </div>

                <div class="execution-container">
                    ${blocksHtml}
                </div>

                <div class="input-container">
                    <textarea id="messageInput" placeholder="Ask me anything about your code..." rows="3"></textarea>
                    <button onclick="sendMessage()" id="sendBtn" ${this.isProcessing ? 'disabled' : ''}>
                        ${this.isProcessing ? '⏳ Processing...' : '🚀 Send'}
                    </button>
                </div>

                <script>
                    ${this.getScript()}
                </script>
            </body>
            </html>
        `;
    }

    /**
     * Render an execution block
     */
    private renderExecutionBlock(block: ExecutionBlock): string {
        const stepsHtml = block.steps.map(step => this.renderStep(step)).join('');
        const toggleIcon = block.collapsed ? '▶' : '▼';

        return `
            <div class="execution-block ${block.status}" data-block-id="${block.id}">
                <div class="block-header" onclick="toggleBlock('${block.id}')">
                    <span class="toggle-icon">${toggleIcon}</span>
                    <span class="block-title">${this.escapeHtml(block.title)}</span>
                    <span class="block-status ${block.status}"></span>
                    <span class="block-time">${block.timestamp}</span>
                </div>
                <div class="block-content ${block.collapsed ? 'collapsed' : ''}">
                    ${stepsHtml}
                </div>
            </div>
        `;
    }

    /**
     * Render a step
     */
    private renderStep(step: ExecutionStep): string {
        let statusIcon = '';
        switch (step.status) {
            case 'pending': statusIcon = '⏳'; break;
            case 'running': statusIcon = '🔄'; break;
            case 'completed': statusIcon = '✅'; break;
            case 'error': statusIcon = '❌'; break;
        }

        const contentHtml = step.content ? `
            <div class="step-content">
                <pre>${this.escapeHtml(step.content)}</pre>
            </div>
        ` : '';

        return `
            <div class="execution-step ${step.status}">
                <div class="step-header">
                    <span class="step-icon">${statusIcon}</span>
                    <span class="step-title">${this.escapeHtml(step.title)}</span>
                    <span class="step-time">${step.timestamp}</span>
                </div>
                ${contentHtml}
            </div>
        `;
    }

    /**
     * Escape HTML
     */
    private escapeHtml(text: string): string {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    /**
     * Get CSS styles
     */
    private getStyles(): string {
        return `
            body {
                font-family: var(--vscode-font-family);
                font-size: var(--vscode-font-size);
                color: var(--vscode-foreground);
                background-color: var(--vscode-editor-background);
                margin: 0;
                padding: 0;
                height: 100vh;
                display: flex;
                flex-direction: column;
            }

            .header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 16px;
                background-color: var(--vscode-titleBar-activeBackground);
                border-bottom: 1px solid var(--vscode-panel-border);
                flex-shrink: 0;
            }

            .header h2 {
                margin: 0;
                color: var(--vscode-titleBar-activeForeground);
                font-size: 16px;
                font-weight: 600;
            }

            .session-info {
                display: flex;
                align-items: center;
                gap: 12px;
                font-size: 12px;
                color: var(--vscode-descriptionForeground);
            }

            .clear-btn {
                background: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border: none;
                padding: 4px 8px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 11px;
            }

            .clear-btn:hover {
                background: var(--vscode-button-hoverBackground);
            }

            .execution-container {
                flex: 1;
                overflow-y: auto;
                padding: 16px;
            }

            .execution-block {
                margin-bottom: 16px;
                border: 1px solid var(--vscode-panel-border);
                border-radius: 8px;
                background-color: var(--vscode-editorWidget-background);
                overflow: hidden;
            }

            .execution-block.completed {
                border-color: var(--vscode-testing-iconPassed);
            }

            .execution-block.error {
                border-color: var(--vscode-errorForeground);
            }

            .execution-block.running {
                border-color: var(--vscode-progressBar-foreground);
            }

            .block-header {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                background: linear-gradient(135deg, var(--vscode-button-background), var(--vscode-button-hoverBackground));
                color: var(--vscode-button-foreground);
                cursor: pointer;
                transition: background-color 0.2s ease;
            }

            .block-header:hover {
                background: var(--vscode-button-hoverBackground);
            }

            .toggle-icon {
                margin-right: 8px;
                font-size: 12px;
                transition: transform 0.2s ease;
            }

            .block-title {
                flex: 1;
                font-weight: 500;
                font-size: 14px;
            }

            .block-status {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                margin-right: 8px;
            }

            .block-status.pending {
                background-color: var(--vscode-charts-yellow);
            }

            .block-status.running {
                background-color: var(--vscode-progressBar-foreground);
                animation: pulse 2s infinite;
            }

            .block-status.completed {
                background-color: var(--vscode-testing-iconPassed);
            }

            .block-status.error {
                background-color: var(--vscode-errorForeground);
            }

            .block-time {
                font-size: 11px;
                opacity: 0.7;
            }

            .block-content {
                max-height: 1000px;
                overflow: hidden;
                transition: max-height 0.3s ease;
            }

            .block-content.collapsed {
                max-height: 0;
            }

            .execution-step {
                padding: 12px 16px;
                border-bottom: 1px solid var(--vscode-panel-border);
            }

            .execution-step:last-child {
                border-bottom: none;
            }

            .execution-step.running {
                background-color: rgba(255, 165, 0, 0.1);
                border-left: 3px solid var(--vscode-progressBar-foreground);
            }

            .execution-step.completed {
                background-color: rgba(0, 255, 0, 0.05);
                border-left: 3px solid var(--vscode-testing-iconPassed);
            }

            .execution-step.error {
                background-color: rgba(255, 0, 0, 0.05);
                border-left: 3px solid var(--vscode-errorForeground);
            }

            .step-header {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .step-icon {
                font-size: 14px;
            }

            .step-title {
                flex: 1;
                font-weight: 500;
                color: var(--vscode-foreground);
            }

            .step-time {
                font-size: 11px;
                color: var(--vscode-descriptionForeground);
                opacity: 0.7;
            }

            .step-content {
                margin-top: 8px;
                padding: 12px;
                background-color: var(--vscode-editor-background);
                border: 1px solid var(--vscode-panel-border);
                border-radius: 4px;
            }

            .step-content pre {
                margin: 0;
                white-space: pre-wrap;
                word-wrap: break-word;
                font-family: var(--vscode-editor-font-family);
                font-size: 13px;
                line-height: 1.4;
            }

            .input-container {
                padding: 16px;
                background-color: var(--vscode-editorWidget-background);
                border-top: 1px solid var(--vscode-panel-border);
                flex-shrink: 0;
            }

            #messageInput {
                width: 100%;
                padding: 12px;
                border: 1px solid var(--vscode-input-border);
                border-radius: 4px;
                background-color: var(--vscode-input-background);
                color: var(--vscode-input-foreground);
                font-family: var(--vscode-font-family);
                font-size: 13px;
                resize: vertical;
                margin-bottom: 8px;
            }

            #messageInput:focus {
                outline: none;
                border-color: var(--vscode-focusBorder);
            }

            #sendBtn {
                width: 100%;
                padding: 10px;
                background: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-weight: 500;
                transition: background-color 0.2s ease;
            }

            #sendBtn:hover:not(:disabled) {
                background: var(--vscode-button-hoverBackground);
            }

            #sendBtn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
        `;
    }

    /**
     * Get JavaScript code
     */
    private getScript(): string {
        return `
            const vscode = acquireVsCodeApi();

            function sendMessage() {
                const input = document.getElementById('messageInput');
                const text = input.value.trim();
                if (text) {
                    vscode.postMessage({
                        command: 'sendMessage',
                        text: text
                    });
                    input.value = '';
                }
            }

            function toggleBlock(blockId) {
                vscode.postMessage({
                    command: 'toggleBlock',
                    blockId: blockId
                });
            }

            function clearHistory() {
                vscode.postMessage({
                    command: 'clearHistory'
                });
            }

            // Handle Enter key in textarea
            document.getElementById('messageInput').addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Auto-focus input
            document.getElementById('messageInput').focus();
        `;
    }
}
