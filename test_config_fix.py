#!/usr/bin/env python3
"""
Test script to verify the continuous execution manager configuration fix.
"""

import sys
import os
sys.path.append('/u/Arun Dev/Python Projects/AI-Coding-Agent')

def test_config_fix():
    """Test that the configuration fix works."""
    
    print("🔧 TESTING CONTINUOUS EXECUTION MANAGER CONFIG FIX")
    print("=" * 60)
    
    try:
        # Test 1: Import the continuous execution manager
        print("\n1️⃣ Testing Import...")
        from bridge.core.continuous_execution_manager import ContinuousExecutionManager
        print("✅ Import successful")
        
        # Test 2: Create instance with None config (this was failing before)
        print("\n2️⃣ Testing Instance Creation with None config...")
        manager = ContinuousExecutionManager(session_manager=None, enhanced_config=None)
        print("✅ Instance creation successful")
        
        # Test 3: Check default values
        print("\n3️⃣ Testing Default Configuration Values...")
        print(f"   Max workers: {manager.max_workers}")
        print(f"   Heartbeat interval: {manager.heartbeat_interval}")
        print(f"   Auto save interval: {manager.auto_save_interval}")
        print(f"   Default execution timeout: {manager.default_execution_timeout}")
        print(f"   Default total timeout: {manager.default_total_timeout}")
        print(f"   Default max timeouts: {manager.default_max_timeouts}")
        
        # Verify expected defaults
        assert manager.max_workers == 4, f"Expected 4, got {manager.max_workers}"
        assert manager.heartbeat_interval == 300, f"Expected 300, got {manager.heartbeat_interval}"
        assert manager.auto_save_interval == 600, f"Expected 600, got {manager.auto_save_interval}"
        assert manager.default_execution_timeout == 1800, f"Expected 1800, got {manager.default_execution_timeout}"
        assert manager.default_total_timeout == 0, f"Expected 0, got {manager.default_total_timeout}"
        assert manager.default_max_timeouts == 20, f"Expected 20, got {manager.default_max_timeouts}"
        
        print("✅ All default values correct")
        
        # Test 4: Test with empty config
        print("\n4️⃣ Testing Instance Creation with Empty config...")
        manager2 = ContinuousExecutionManager(session_manager=None, enhanced_config={})
        print("✅ Empty config instance creation successful")
        
        # Test 5: Test with partial config
        print("\n5️⃣ Testing Instance Creation with Partial config...")
        partial_config = {
            "continuous": {
                "parallel": {"max_workers": 8},
                "agent": {"heartbeat_interval": 600}
            }
        }
        manager3 = ContinuousExecutionManager(session_manager=None, enhanced_config=partial_config)
        print(f"   Max workers: {manager3.max_workers} (should be 8)")
        print(f"   Heartbeat interval: {manager3.heartbeat_interval} (should be 600)")
        print(f"   Auto save interval: {manager3.auto_save_interval} (should be 600 - default)")
        
        assert manager3.max_workers == 8, f"Expected 8, got {manager3.max_workers}"
        assert manager3.heartbeat_interval == 600, f"Expected 600, got {manager3.heartbeat_interval}"
        assert manager3.auto_save_interval == 600, f"Expected 600, got {manager3.auto_save_interval}"
        
        print("✅ Partial config values correct")
        
        print("\n" + "=" * 60)
        print("🎉 ALL CONFIGURATION TESTS PASSED!")
        print("=" * 60)
        
        print("\n✅ FIXES VERIFIED:")
        print("• Safe configuration access with None handling")
        print("• Proper default value assignment")
        print("• Graceful handling of missing config sections")
        print("• Backward compatibility with existing configs")
        
        print("\n🚀 NEXT STEPS:")
        print("1. Restart the bridge server to load the fixed code:")
        print("   cd bridge && python api_server.py")
        print("2. Test the VS Code extension with SWE-Agent integration")
        print("3. Verify that continuous sessions can be created")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_config_fix()
    
    if success:
        print("\n🎯 CONFIGURATION FIX VERIFIED!")
        print("The continuous execution manager can now be safely imported and used.")
    else:
        print("\n⚠️  Configuration fix needs more work.")
