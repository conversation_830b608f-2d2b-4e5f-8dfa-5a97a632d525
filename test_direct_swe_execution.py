#!/usr/bin/env python3
"""
Direct test of SWE-Agent execution without complex bridge imports.
"""

import os
import sys
import subprocess
import tempfile
import time
from pathlib import Path

def test_direct_swe_execution():
    """Test SWE-Agent execution directly."""
    print("🧪 Testing Direct SWE-Agent Execution")
    print("=" * 50)
    
    # Setup paths
    project_root = Path.cwd()
    swe_venv = project_root / "swe_venv" / "bin" / "python"
    swe_agent_path = project_root / "swe-agent"
    
    print(f"Project root: {project_root}")
    print(f"SWE venv: {swe_venv}")
    print(f"SWE agent: {swe_agent_path}")
    
    # Check prerequisites
    if not swe_venv.exists():
        print("❌ SWE-Agent virtual environment not found")
        return False
        
    if not swe_agent_path.exists():
        print("❌ SWE-Agent directory not found")
        return False
        
    print("✅ Prerequisites found")
    
    # Test SWE-Agent CLI
    print("\n🔍 Testing SWE-Agent CLI...")
    try:
        result = subprocess.run([
            str(swe_venv), "-m", "sweagent", "--help"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ SWE-Agent CLI is working")
        else:
            print(f"❌ SWE-Agent CLI failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ SWE-Agent CLI test timed out")
        return False
    except Exception as e:
        print(f"❌ SWE-Agent CLI test failed: {e}")
        return False
    
    # Create a simple config file with correct format
    print("\n📝 Creating test configuration...")
    config_data = """
agent:
  templates:
    system_template: |-
      You are a helpful assistant that can interact with a computer to solve tasks.
    instance_template: |-
      I need you to help me with the following task:
      {{problem_statement}}

      Please use the available tools to complete this task.
    next_step_template: |-
      OBSERVATION:
      {{observation}}
  tools:
    env_variables:
      WINDOW: 100
      OVERLAP: 2
    bundles:
      - path: tools/registry
      - path: tools/windowed
      - path: tools/search
      - path: tools/submit
    enable_bash_tool: true
    parse_function:
      type: function_calling
  history_processors:
    - type: last_n_observations
      n: 5
"""
    
    # Write config to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        f.write(config_data)
        config_file = f.name
    
    print(f"✅ Config file created: {config_file}")
    
    # Create output directory
    output_dir = f"/tmp/swe_test_output_{int(time.time())}"
    os.makedirs(output_dir, exist_ok=True)
    print(f"✅ Output directory: {output_dir}")
    
    # Test SWE-Agent execution
    print("\n🚀 Testing SWE-Agent execution...")
    
    cmd = [
        str(swe_venv),
        "-m", "sweagent", "run",
        "--config", config_file,
        "--output_dir", output_dir,
        "--problem_statement", "List all Python files in the current directory and show their sizes using ls -la *.py"
    ]
    
    print(f"Command: {' '.join(cmd)}")
    
    # Set up environment
    env = os.environ.copy()
    
    # Check for API keys
    if "ANTHROPIC_API_KEY" not in env and "OPENAI_API_KEY" not in env:
        print("⚠️  No API keys found in environment")
        print("   Set ANTHROPIC_API_KEY or OPENAI_API_KEY to test with real LLM")
        print("   Continuing with test anyway...")
    
    try:
        # Start the process
        print("Starting SWE-Agent process...")
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=env,
            cwd=str(swe_agent_path)
        )
        
        print(f"✅ Process started with PID: {process.pid}")
        
        # Monitor output for a limited time
        start_time = time.time()
        timeout = 60  # 1 minute timeout for test
        
        while time.time() - start_time < timeout:
            # Check if process is still running
            if process.poll() is not None:
                break
                
            # Read some output
            try:
                stdout_line = process.stdout.readline()
                if stdout_line:
                    print(f"STDOUT: {stdout_line.strip()}")
                    
                stderr_line = process.stderr.readline()
                if stderr_line:
                    print(f"STDERR: {stderr_line.strip()}")
                    
            except:
                pass
                
            time.sleep(0.5)
        
        # Get final status
        if process.poll() is None:
            print("⏰ Process still running after timeout, terminating...")
            process.terminate()
            try:
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
        
        # Get final output
        stdout, stderr = process.communicate()
        return_code = process.returncode
        
        print(f"\n📊 Final Results:")
        print(f"Return code: {return_code}")
        
        if stdout:
            print(f"Final stdout:\n{stdout}")
            
        if stderr:
            print(f"Final stderr:\n{stderr}")
        
        # Check output directory
        output_files = list(Path(output_dir).glob("*"))
        print(f"Output files created: {len(output_files)}")
        for f in output_files[:5]:  # Show first 5 files
            print(f"  - {f.name}")
        
        if return_code == 0:
            print("✅ SWE-Agent execution completed successfully")
            return True
        else:
            print(f"⚠️  SWE-Agent execution completed with return code {return_code}")
            return True  # Still consider it a success if it ran
            
    except Exception as e:
        print(f"❌ SWE-Agent execution failed: {e}")
        return False
        
    finally:
        # Cleanup
        try:
            os.unlink(config_file)
        except:
            pass

def main():
    """Main test function."""
    success = test_direct_swe_execution()
    
    if success:
        print("\n🎉 Direct SWE-Agent execution test completed!")
        print("   The SWE-Agent can be executed successfully.")
    else:
        print("\n❌ Direct SWE-Agent execution test failed!")
        print("   There are issues with the SWE-Agent setup.")
    
    return success

if __name__ == "__main__":
    main()
