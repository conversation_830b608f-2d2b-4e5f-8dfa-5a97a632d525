# Agent Timeout Issues Analysis

## Root Causes of Premature Agent Termination

### 1. **SWE-Agent Tool Configuration Timeouts**

**File:** `swe-agent/sweagent/tools/tools.py` (Lines 132-145)

```python
execution_timeout: int = 30
"""Timeout for executing commands in the environment"""

install_timeout: int = 300
"""Timeout used for each of the installation commands"""

total_execution_timeout: int = 1800
"""Timeout for executing all commands in the environment.
Note: Does not interrupt running commands, but will stop the agent for the next step.
"""

max_consecutive_execution_timeouts: int = 3
"""Maximum number of consecutive execution timeouts before the agent exits.
"""
```

**Issues:**
- `execution_timeout: 30` seconds - Too short for complex operations
- `total_execution_timeout: 1800` seconds (30 minutes) - Agent stops after 30 minutes total
- `max_consecutive_execution_timeouts: 3` - <PERSON> exits after 3 consecutive timeouts

### 2. **Agent Execution Logic Timeouts**

**File:** `swe-agent/sweagent/agent/agents.py` (Lines 952-959)

```python
except CommandTimeoutError:
    self._n_consecutive_timeouts += 1
    if self._n_consecutive_timeouts >= self.tools.config.max_consecutive_execution_timeouts:
        msg = "Exiting agent due to too many consecutive execution timeouts"
        self.logger.critical(msg)
        step.execution_time = time.perf_counter() - execution_t0
        self._total_execution_time += step.execution_time
        raise
```

### 3. **Bridge Client HTTP Timeout**

**File:** `vscode-extension/src/bridge-client.ts` (Line 70)

```typescript
timeout: 300000, // Increased to 5 minutes for LLM responses
```

**Issue:** 5-minute HTTP timeout may interrupt long-running agent operations

### 4. **Environment Communication Timeout**

**File:** `swe-agent/sweagent/environment/swe_env.py` (Line 200)

```python
timeout: int | float = 25,
```

**Issue:** 25-second default timeout for environment communication

## Solutions to Enable Continuous Execution

### 1. **Increase SWE-Agent Timeouts**
- Increase `execution_timeout` from 30 to 300 seconds
- Increase `total_execution_timeout` from 1800 to unlimited (0 or very large value)
- Increase `max_consecutive_execution_timeouts` from 3 to 10

### 2. **Implement Continuous Execution Mode**
- Add configuration option for "continuous mode"
- Disable total execution timeout in continuous mode
- Add heartbeat mechanism to keep sessions alive

### 3. **Parallel Task Support**
SWE-Agent does support parallel execution through:
- `num_workers` parameter in batch runs
- Multiple session management in bridge

### 4. **Session Management Improvements**
- Implement session persistence
- Add session resumption capability
- Better error recovery and retry mechanisms

## Recommended Configuration Changes

### For Continuous Execution:
```yaml
agent:
  tools:
    execution_timeout: 300  # 5 minutes per command
    total_execution_timeout: 0  # Unlimited
    max_consecutive_execution_timeouts: 10
  max_iterations: 0  # Unlimited iterations
```

### For Parallel Tasks:
```yaml
batch:
  num_workers: 4  # Run 4 agents in parallel
  random_delay_multiplier: 0.1
```

## Files That Need Modification

1. **swe-agent/sweagent/tools/tools.py** - Increase timeout values
2. **bridge/core/enhanced_config.py** - Add continuous mode configuration ✅ COMPLETED
3. **bridge/integrations/swe_agent_interface.py** - Implement continuous execution
4. **vscode-extension/src/bridge-client.ts** - Increase HTTP timeouts ✅ COMPLETED
5. **bridge/core/session_manager.py** - Add session persistence and resumption

## Implementation Status

### ✅ COMPLETED:
1. **Enhanced Configuration** - Added "continuous" mode in `bridge/core/enhanced_config.py`
2. **Continuous Execution Manager** - Created `bridge/core/continuous_execution_manager.py`
3. **Bridge Client Updates** - Extended timeout and added continuous mode methods
4. **API Endpoints** - Added continuous session endpoints in `bridge/api/api_server.py`
5. **Configuration File** - Created `bridge/config/continuous_swe_config.yaml`
6. **Startup Script** - Created `start_continuous_mode.py`

### 🚧 NEXT STEPS:
1. **Override SWE-Agent Timeouts** - Apply configuration overrides
2. **Test Continuous Mode** - Verify the implementation works
3. **VS Code Integration** - Update extension to use continuous mode

## How to Use Continuous Mode

### 1. Start Continuous Mode Server:
```bash
python start_continuous_mode.py
```

### 2. Create Continuous Session via API:
```bash
curl -X POST http://localhost:8080/api/sessions/continuous \
     -H "Content-Type: application/json" \
     -d '{
       "problem_statement": "Your complex task here",
       "repo_path": "/path/to/your/repo",
       "model_name": "claude-sonnet-4-20250514",
       "enable_parallel": true
     }'
```

### 3. Use VS Code Extension:
The bridge client now has `runAgentContinuous()` method that enables unlimited execution.

## Key Features Implemented

### 1. **Unlimited Execution Time**
- `total_execution_timeout: 0` (unlimited)
- `execution_timeout: 600` (10 minutes per command)
- `max_consecutive_execution_timeouts: 10` (more tolerance)

### 2. **Parallel Task Support**
- Multi-worker execution (4 workers by default)
- Sub-task creation and management
- Priority-based task queue

### 3. **Session Persistence**
- Auto-save every 10 minutes
- Session resumption capability
- Heartbeat monitoring

### 4. **Enhanced Error Handling**
- Circuit breaker pattern
- Exponential backoff retry
- Automatic error recovery

### 5. **Real-time Monitoring**
- WebSocket progress updates
- Performance metrics
- Debug logging
